<!--审核详情页-->
<view class="container">
  <!-- 审核状态栏 -->
  <view class="status-bar {{reviewData.status}}">
    <view class="status-icon"></view>
    <view class="status-text">{{reviewData.statusText}}</view>
  </view>

  <!-- 申请信息卡片 -->
  <view class="detail-card">
    <view class="card-title">申请信息</view>
    <view class="info-list">
      <view class="info-item">
        <view class="info-label">申请人</view>
        <view class="info-value">{{reviewData.residentName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">手机号</view>
        <view class="info-value">{{reviewData.phone}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">申请时间</view>
        <view class="info-value">{{reviewData.createTime}}</view>
      </view>
      <view class="info-item" wx:if="{{reviewData.residentTypeName}}">
        <view class="info-label">居民类型</view>
        <view class="info-value">{{reviewData.residentTypeName}}</view>
      </view>

      <!-- 根据类型显示不同信息 -->
      <block wx:if="{{reviewData.type === 'resident_room'}}">
        <view class="info-item">
          <view class="info-label">房产地址</view>
          <view class="info-value">{{reviewData.addressText}}</view>
        </view>
        <view class="info-item" wx:if="{{reviewData.buildingNumber}}">
          <view class="info-label">楼栋号</view>
          <view class="info-value">{{reviewData.buildingNumber}}</view>
        </view>
        <view class="info-item" wx:if="{{reviewData.unitNumber}}">
          <view class="info-label">单元号</view>
          <view class="info-value">{{reviewData.unitNumber}}</view>
        </view>
        <view class="info-item" wx:if="{{reviewData.roomNumber}}">
          <view class="info-label">房间号</view>
          <view class="info-value">{{reviewData.roomNumber}}</view>
        </view>
      </block>

      <block wx:elif="{{reviewData.type === 'resident_vehicle'}}">
        <view class="info-item">
          <view class="info-label">车牌号</view>
          <view class="info-value">{{reviewData.plateNumber}}</view>
        </view>
      </block>
    </view>
  </view>

  
  <!-- 审核操作区 -->
  <view class="detail-card">
    <view class="card-title">审核操作</view>
    <view class="review-actions">
      <view class="status-selector" bindtap="showStatusSelector">
        <view class="selector-label">审核状态</view>
        <view class="selector-value">{{selectedStatusName || '请选择审核状态'}}</view>
        <view class="selector-arrow">></view>
      </view>
      <button class="btn-submit" bindtap="submitReview" disabled="{{submitting || !selectedStatus}}">
        {{submitting ? '提交中...' : '提交审核'}}
      </button>
    </view>
  </view>

  <!-- 状态选择弹窗 -->
  <view class="modal {{showStatusModal ? 'show' : ''}}" catchtouchmove="preventTouchMove">
    <view class="modal-mask" bindtap="closeStatusModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择审核状态</text>
      </view>
      <view class="modal-body">
        <view class="status-list">
          <view class="status-item" wx:for="{{statusOptions}}" wx:key="nameEn"
                bindtap="selectStatus" data-status="{{item.nameEn}}">
            <text class="status-name">{{item.nameCn}}</text>
            <view class="status-check {{selectedStatus === item.nameEn ? 'checked' : ''}}"></view>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeStatusModal">取消</button>
      </view>
    </view>
  </view>
</view>
