
const REQUEST = require('../utils/request.js')

/**
 * 工单模块API接口
 * 基于接口文档：/users-api/v1/member/work-order
 */

// ==================== 工单管理 ====================

/**
 * 分页查询工单列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 * @param {string} params.status - 工单状态（可选）
 * @param {string} params.type - 工单类型（可选）
 * @returns {Promise} 工单列表
 */
function getWorkOrderList(params = {}) {
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }

  // 添加可选参数
  if (params.status) {
    queryParams.status = params.status
  }
  if (params.type) {
    queryParams.type = params.type
  }
  queryParams.communityId = params.communityId
  return REQUEST.request('/users-api/v1/member/work-order/page', 'GET', queryParams, true)
}

/**
 * 获取工单详情
 * @param {number} id - 工单ID
 * @returns {Promise} 工单详情
 */
function getWorkOrderDetail(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'))
  }
  return REQUEST.request('/users-api/v1/member/work-order?id=' + id, 'GET', {}, true)
}

/**
 * 新增工单
 * @param {Object} data - 工单数据
 * @param {string} data.type - 工单类型
 * @param {string} data.userDescribe - 问题描述
 * @param {string} data.media - 图片列表（多个用逗号分隔）
 * @param {string} data.regionType - 区域类型
 * @param {string} data.region - 区域描述
 * @returns {Promise} 创建结果
 */
function createWorkOrder(data) {
  const requestData = {
    type: data.type,
    userDescribe: data.userDescribe,
    media: data.media || '',
    regionType: data.regionType,
    region: data.region,
    communityId: data.communityId,
  }

  return REQUEST.request('/users-api/v1/member/work-order', 'POST', requestData, true)
}

/**
 * 取消工单
 * @param {number} id - 工单ID
 * @returns {Promise} 取消结果
 */
function cancelWorkOrder(id) {
  if (!id) {
    return Promise.reject(new Error('工单ID不能为空'))
  }

  const requestData = {
    id: id
  }

  return REQUEST.request('/users-api/v1/member/work-order/cancel', 'PUT', requestData, true)
}


//物业工作台工单列表
function getPropertyWorkOrderList(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/page', 'GET', params, true)
}


//物业工作台取消工单
function propertyCancelWorkOrder(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/cancel', 'PUT', params, true)
}

//物业查询工单详情
function getPropertyWorkOrderDetail(id)
{
  return  REQUEST.request('/manage-api/v1/community/work-order?id='+id, 'GET', {}, true)
}


//物业工作台获取可分配员工列表
function getPropertyPrsonList(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/person', 'GET', params, true)
}

//物业受理工单
function propertyAcceptedWorkOrder(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/accepted', 'PUT', params, true)
}

//物业处理工单
function propertyProcessingWorkOrder(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/processing', 'PUT', params, true)
}

//物业挂起工单
function propertyPendingWorkOrder(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/pending', 'PUT', params, true)
}

//物业完成工单
function propertyCompleteWorkOrder(id)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/complete', 'PUT', params, true)
}

//类型分布统计统计
function getPropertyTypeCount(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/type-count', 'GET', params, true)
}

//概览统计
function getPropertyStatusCount(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/status-count', 'GET', params, true)
}

//趋势分析统计
function getPropertyDataCount(params)
{
  return  REQUEST.request('/manage-api/v1/community/work-order/data-count', 'GET', params, true)
}

module.exports = {
  getWorkOrderList,
  getWorkOrderDetail,
  createWorkOrder,
  cancelWorkOrder,
  getPropertyWorkOrderList,
  getPropertyWorkOrderDetail,
  getPropertyPrsonList,
  propertyAcceptedWorkOrder,
  propertyProcessingWorkOrder,
  propertyPendingWorkOrder,
  propertyCompleteWorkOrder,
  propertyCancelWorkOrder,
}