/* 公告发布/编辑页样式 */

.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.form-container {
  padding: 20rpx;
}

.form-item {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
}

.form-label {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

.form-input {
  width: calc(100% - 40rpx);
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #333;
}

.form-input:focus {
  border-color: #ff8c00;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.picker-text {
  font-size: 30rpx;
  color: #333;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 30rpx;
  color: #333;
}

.form-textarea:focus {
  border-color: #ff8c00;
}

.selected-buildings {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.building-tag {
  background-color: #ff8c00;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.image-upload {
  margin-top: 20rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-top: 2rpx solid #e8e8e8;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
}

.draft-btn {
  background-color: #f5f5f5;
  color: #666;
}

.publish-btn {
  background-color: #ff8c00;
  color: #fff;
}

.publish-btn:disabled {
  background-color: #ccc;
  color: #999;
}

/* 草稿箱弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.draft-modal {
  width: 80%;
  max-height: 70%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #e8e8e8;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.draft-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.draft-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.draft-content {
  flex: 1;
}

.draft-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.draft-time {
  font-size: 24rpx;
  color: #999;
}

.draft-delete {
  color: #ff4d4f;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.empty-draft {
  padding: 60rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 暗黑模式 */
.dark .container {
  background-color: #1a1a1a;
}

.dark .form-item {
  background-color: #2a2a2a;
}

.dark .form-label {
  color: #e8e8e8;
}

.dark .form-input,
.dark .form-textarea {
  background-color: #333;
  border-color: #444;
  color: #e8e8e8;
}

.dark .form-picker {
  background-color: #333;
  border-color: #444;
}

.dark .picker-text {
  color: #e8e8e8;
}

.dark .upload-btn {
  background-color: #333;
  border-color: #444;
  color: #999;
}

.dark .bottom-actions {
  background-color: #2a2a2a;
  border-top-color: #444;
}

.dark .draft-modal {
  background-color: #2a2a2a;
}

.dark .modal-header {
  border-bottom-color: #444;
}

.dark .modal-title {
  color: #e8e8e8;
}

.dark .draft-item {
  border-bottom-color: #444;
}

.dark .draft-title {
  color: #e8e8e8;
}

/* 楼栋选择弹窗 */
.building-modal {
  width: 85%;
  max-height: 75%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.building-list {
  flex: 1;
  max-height: 500rpx;
  overflow-y: auto;
  padding: 0 30rpx;
}

.building-item {
  padding: 25rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  display: flex;
  align-items: center;
}

.building-item:last-child {
  border-bottom: none;
}

.building-item checkbox-group {
  width: 100%;
  display: flex;
  align-items: center;
}

.building-item checkbox {
  transform: scale(1.2);
  margin-right: 20rpx;
}

.empty-building {
  padding: 60rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #e8e8e8;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  font-size: 30rpx;
  font-weight: 500;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #ff8c00;
  color: #fff;
}

/* 楼栋选择弹窗暗黑模式 */
.dark .building-modal {
  background-color: #2a2a2a;
}

.dark .building-item {
  border-bottom-color: #444;
}

.dark .modal-footer {
  border-top-color: #444;
}

.dark .cancel-btn {
  background-color: #333;
  color: #e8e8e8;
}
