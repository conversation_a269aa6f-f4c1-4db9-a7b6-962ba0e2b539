<!--居民管理主页-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="title">居民管理</view>
    <view class="subtitle">高效管理社区居民信息</view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-card" animation="{{animationData}}">
    <!-- <view class="stats-header">
      <view class="stats-more" bindtap="navigateToStatistics">查看详细统计</view>
    </view> -->
    <view class="stats-content">
      <view class="stats-item">
        <view class="stats-value">{{statistics.totalResidents}}</view>
        <view class="stats-label">总人数</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{statistics.pendingReviews}}</view>
        <view class="stats-label">待审核</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{statistics.todayNew}}</view>
        <view class="stats-label">今日新增</view>
      </view>
    </view>
  </view>

  <!-- 快速操作区 -->
  <view class="quick-actions" animation="{{animationData}}">
    <view class="section-title">快速操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="navigateToReview">
        <view class="action-icon review-icon"></view>
        <view class="action-label">信息审核</view>
      </view>
      <view class="action-item" bindtap="navigateToSearch">
        <view class="action-icon search-icon"></view>
        <view class="action-label">居民查询</view>
      </view>
      <view class="action-item" bindtap="navigateToAdd">
        <view class="action-icon add-icon"></view>
        <view class="action-label">添加居民</view>
      </view>
      <view class="action-item" bindtap="navigateToStatistics">
        <view class="action-icon stats-icon"></view>
        <view class="action-label">数据统计</view>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="recent-activities" animation="{{animationData}}">
    <view class="section-title">最近活动</view>
    <view class="activity-list">
      <view class="activity-item" wx:for="{{activities}}" wx:key="id" bindtap="navigateToActivity" data-id="{{item.id}}" data-type="{{item.type}}">
        <view class="activity-icon {{item.iconClass}}"></view>
        <view class="activity-content">
          <view class="activity-text">{{item.text}}</view>
          <view class="activity-time">{{item.time}}</view>
        </view>
        <view class="activity-arrow"></view>
      </view>
    </view>
  </view>
</view>
