// pages/property/workorder/process/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const util = require('@/utils/util.js');

Page({
  data: {
    darkMode: false,
    workOrder: null,
    loading: true,
    submitting: false,

    // 页面模式：accept-受理工单
    mode: 'accept',

    // 员工列表
    staffList: [],
    selectedStaffIds: [], // 多选员工ID

    // 字典数据
    personWorkStatusDict: [], // 员工工作状态字典

    // 状态栏高度
    statusBarHeight: 20,
  },

  onLoad: function(options) {
    const { id, mode } = options;

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏样式和模式
    this.setData({
      statusBarHeight: statusBarHeight,
      mode: mode || 'accept'
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '受理工单'
    });

    // 初始化字典数据
    this.initDictData();

    if (id) {
      this.loadWorkOrderDetail(id);
      this.loadStaffList();
    } else {
      wx.showToast({
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const personWorkStatusDict = util.getDictByNameEn('person_work_status')[0].children;

      this.setData({
        personWorkStatusDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载工单详情
  loadWorkOrderDetail: function(id) {
    this.setData({ loading: true });

    workOrderApi.getPropertyWorkOrderDetail(id)
      .then(order => {
        console.log('工单详情数据：', order);
        this.setData({
          workOrder: order,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单详情失败', error);
        this.setData({ loading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载员工列表
  loadStaffList: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 100,
      communityId: selectedCommunity.id
    };

    workOrderApi.getPropertyPrsonList(params)
      .then(res => {
        console.log('获取员工列表成功', res);

        // 为员工添加选中状态和工作状态显示，参考员工管理列表的显示格式
        const staffList = (res || []).map(staff => ({
          ...staff,
          selected: false,
          workStatusText: this.getWorkStatusText(staff.workStatus),
          // 添加员工管理列表中的显示字段
          name: staff.personName || staff.name || '',
          employeeId: staff.personNumber || staff.employeeId || '',
          organization: staff.orgName || staff.organization || '',
          position: staff.positionName || staff.position || '',
          phone: staff.phone || '',
          gender: staff.gender || '',
          age: staff.age || 0
        }));

        this.setData({
          staffList
        });
      })
      .catch(err => {
        console.error('获取员工列表失败', err);
        wx.showToast({
          title: '获取员工列表失败',
          icon: 'none'
        });
      });
  },

  // 获取员工工作状态文本
  getWorkStatusText: function(workStatus) {
    const statusDict = this.data.personWorkStatusDict;
    const statusItem = statusDict.find(item => item.nameEn === workStatus);
    return statusItem ? statusItem.nameCn : (workStatus || '未知');
  },

  // 选择/取消选择员工
  toggleStaffSelection: function(e) {
    const staffId = e.currentTarget.dataset.id;
    const staffList = this.data.staffList.map(staff => {
      if (staff.id === staffId) {
        return { ...staff, selected: !staff.selected };
      }
      return staff;
    });

    // 更新选中的员工ID列表
    const selectedStaffIds = staffList.filter(staff => staff.selected).map(staff => staff.id);

    this.setData({
      staffList,
      selectedStaffIds
    });
  },

  // 提交受理工单
  submitAccept: function() {
    if (this.data.selectedStaffIds.length === 0) {
      wx.showToast({
        title: '请选择至少一个员工',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认受理',
      content: `确定要将工单分配给选中的${this.data.selectedStaffIds.length}个员工吗？`,
      success: (res) => {
        if (res.confirm) {
          this.doSubmitAccept();
        }
      }
    });
  },

  // 执行提交受理
  doSubmitAccept: function() {
    const params = {
      id: this.data.workOrder.id,
      personIds: this.data.selectedStaffIds
    };

    this.setData({ submitting: true });
    wx.showLoading({
      title: '受理中...'
    });

    workOrderApi.propertyAcceptedWorkOrder(params)
      .then(() => {
        wx.hideLoading();
        this.setData({ submitting: false });

        wx.showToast({
          title: '工单受理成功',
          icon: 'success'
        });

        setTimeout(() => {
          // 设置标记，通知详情页面需要刷新
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2]; // 获取上一个页面
          if (prevPage && prevPage.route.includes('workorder/detail')) {
            // 如果上一个页面是工单详情页，调用其刷新方法
            prevPage.loadWorkOrderDetail(this.data.workOrder.id);
          }
          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        wx.hideLoading();
        this.setData({ submitting: false });

        console.error('受理工单失败', error);
        wx.showToast({
          title: '受理失败，请重试',
          icon: 'none'
        });
      });
  },
  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
