/**
 * 公告发布/编辑页
 * 用于创建新公告或编辑现有公告/草稿
 */

const noticeApi = require('../../../../../api/noticeApi');
const communityApi = require('../../../../../api/communityApi');
const commApi = require('../../../../../api/commApi');
const util = require('../../../../../utils/util');
const dateUtil = require('../../../../../utils/dateUtil');

Page({
  data: {
    darkMode: false,
    mode: 'create', // create-创建新公告，edit-编辑公告
    isDraftEdit: false, // 是否是编辑草稿
    sourceTab: '', // 来源tab，用于返回时刷新对应列表
    apiUrl: '', // API基础URL
    formData: {
      id: null, // 公告ID，编辑模式下有值
      title: '', // 公告标题
      type: 'property_notice', // 公告类型
      targetType: 'community', // 发布对象类型：community-全体业主, building-楼栋
      targetIds: '', // 目标ID，逗号分隔
      content: '', // 公告内容
      imageUrl: '', // 图片URL
      sort: 0, // 排序
      top: false // 是否置顶
    },
    noticeTypeDict: [], // 通知类型字典
    noticeTargetDict: [], // 通知对象字典
    buildingList: [], // 楼栋列表
    selectedBuildings: [], // 已选楼栋列表
    typeIndex: 0, // 当前选中的公告类型索引
    targetIndex: 0, // 当前选中的发布对象索引
    isSubmitting: false, // 是否正在提交
    showDraftModal: false, // 是否显示草稿弹窗
    draftList: [], // 草稿列表
    showBuildingModal: false, // 是否显示楼栋选择弹窗
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置API URL
    const apiUrl = wx.getStorageSync('apiUrl');
    this.setData({ apiUrl });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: options.mode === 'edit' ? '编辑公告' : '发布公告'
    });

    // 设置模式
    if (options.mode) {
      this.setData({ mode: options.mode });
    }

    // 初始化字典数据
    this.initDictData();

    // 获取来源tab信息
    this.getSourceTabFromPrevPage();

    // 如果是编辑模式，根据来源加载不同数据
    if (options.id && options.mode === 'edit') {
      const isDraft = options.isDraft === 'true'; // 是否是草稿
      this.setData({ isDraftEdit: isDraft });

      if (isDraft) {
        this.loadDraftData(options.id);
      } else {
        this.loadNoticeData(options.id);
      }
    }
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const noticeTypeDict = util.getDictByNameEn('notice_type')[0].children;
      const noticeTargetDict = util.getDictByNameEn('notice_target')[0].children;

      this.setData({
        noticeTypeDict,
        noticeTargetDict
      });

      // 设置默认的targetType为community（全体业主）
      const selectedCommunity = wx.getStorageSync('selectedCommunity');
      if (selectedCommunity && selectedCommunity.id) {
        // 找到community对应的索引
        const communityIndex = noticeTargetDict.findIndex(item => item.nameEn === 'community');

        this.setData({
          'formData.targetType': 'community',
          'formData.targetIds': selectedCommunity.id,
          targetIndex: communityIndex >= 0 ? communityIndex : 0
        });
      }
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载草稿数据（编辑模式）
  loadDraftData: function(draftId) {
    wx.showLoading({
      title: '加载草稿中...',
      mask: true
    });

    // 先获取草稿列表
    noticeApi.getDraftList()
      .then(res => {
        const draftList = res.data || res.list || [];
        console.log('获取草稿列表:', draftList);

        // 根据ID查找对应的草稿
        const draft = draftList.find(item => item.id === draftId);

        if (!draft) {
          wx.hideLoading();
          wx.showToast({
            title: '草稿不存在',
            icon: 'none'
          });
          return;
        }

        console.log('找到草稿:', draft);

        // 设置表单数据
        const formData = {
          id: draft.id,
          title: draft.title || '',
          type: draft.type || 'property_notice',
          targetType: draft.targetType || 'community',
          targetIds: draft.targetIds || '',
          content: draft.content || '',
          imageUrl: draft.imageUrl || '',
          sort: draft.sort || 0,
          top: draft.top || false
        };

        // 设置类型和发布对象的索引
        const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === formData.type);
        const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === formData.targetType);

        this.setData({
          formData,
          selectedBuildings: [],
          typeIndex: typeIndex !== -1 ? typeIndex : 0,
          targetIndex: targetIndex !== -1 ? targetIndex : 0
        });

        // 如果是楼栋类型，需要加载楼栋列表并恢复选择状态
        if (formData.targetType === 'building') {
          this.restoreBuildingSelection(formData.targetIds);
        }

        wx.hideLoading();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('加载草稿数据失败', error);
        wx.showToast({
          title: '加载草稿失败，请重试',
          icon: 'none'
        });
      });
  },

  // 获取来源页面的tab信息
  getSourceTabFromPrevPage: function() {
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];

      // 如果上一个页面是公告管理页面，获取其activeTab
      if (prevPage.route.includes('announcement/announcement')) {
        const sourceTab = prevPage.data.activeTab;
        this.setData({ sourceTab: sourceTab });
        console.log('获取来源tab:', sourceTab);
      }
    }
  },

  // 加载草稿数据（编辑草稿）
  loadDraftData: function(draftId) {
    console.log('开始加载草稿数据，ID:', draftId);

    // 先检查当前是否已有草稿列表
    if (this.data.draftList && this.data.draftList.length > 0) {
      console.log('使用已有的草稿列表');
      const draft = this.data.draftList.find(item => item.id == draftId);
      if (draft) {
        console.log('从已有列表中找到草稿:', draft);
        this.populateFormData(draft);
        return;
      }
    }

    wx.showLoading({
      title: '加载草稿中...',
      mask: true
    });

    // 获取草稿列表
    noticeApi.getDraft()
      .then(res => {
        console.log('草稿API返回结果:', res);

        // 处理不同的返回数据结构
        let draftList = [];
        if (res.data && Array.isArray(res.data)) {
          draftList = res.data;
        } else if (res.list && Array.isArray(res.list)) {
          draftList = res.list;
        } else if (Array.isArray(res)) {
          draftList = res;
        }

        console.log('解析后的草稿列表:', draftList);
        console.log('草稿列表长度:', draftList.length);

        // 保存草稿列表到data中
        this.setData({ draftList: draftList });

        if (draftList.length === 0) {
          wx.hideLoading();
          wx.showToast({
            title: '暂无草稿数据',
            icon: 'none'
          });
          return;
        }

        // 根据ID查找对应的草稿
        console.log('查找草稿ID:', draftId, '类型:', typeof draftId);
        const draft = draftList.find(item => {
          console.log('对比草稿:', item.id, '类型:', typeof item.id);
          return item.id == draftId; // 使用==避免类型问题
        });

        if (!draft) {
          wx.hideLoading();
          console.error('未找到匹配的草稿');
          wx.showToast({
            title: '草稿不存在',
            icon: 'none'
          });
          return;
        }

        console.log('找到草稿:', draft);
        this.populateFormData(draft);
        wx.hideLoading();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('加载草稿数据失败', error);
        wx.showToast({
          title: '加载草稿失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载通知数据（编辑通知）
  loadNoticeData: function(noticeId) {
    wx.showLoading({
      title: '加载通知中...',
      mask: true
    });

    noticeApi.getNotice(noticeId)
      .then(res => {
        const notice = res.data || res;
        console.log('获取通知数据:', notice);
        this.populateFormData(notice);
        wx.hideLoading();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('加载通知数据失败', error);
        wx.showToast({
          title: '加载通知失败，请重试',
          icon: 'none'
        });
      });
  },

  // 填充表单数据（通用方法）
  populateFormData: function(data) {
    console.log('填充表单数据:', data);

    // 设置表单数据
    const formData = {
      id: data.id,
      title: data.title || '',
      type: data.type || 'property_notice',
      targetType: data.targetType || 'community',
      targetIds: data.targetIds || '',
      content: data.content || '',
      imageUrl: data.imageUrl || '',
      sort: data.sort || 0,
      top: data.top || false
    };

    // 设置类型和发布对象的索引
    const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === formData.type);
    const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === formData.targetType);

    this.setData({
      formData,
      typeIndex: typeIndex !== -1 ? typeIndex : 0,
      targetIndex: targetIndex !== -1 ? targetIndex : 0
    });

    // 处理楼栋选择状态
    if (formData.targetType === 'building' && formData.targetIds) {
      console.log('恢复楼栋选择状态，targetIds:', formData.targetIds);
      this.restoreBuildingSelection(formData.targetIds);
    } else {
      // 清空楼栋选择
      this.setData({
        selectedBuildings: [],
        buildingList: this.data.buildingList.map(item => ({ ...item, selected: false }))
      });
    }
  },

  // 恢复楼栋选择状态（编辑模式）
  restoreBuildingSelection: function(targetIds) {
    if (!targetIds) {
      console.log('targetIds为空，清空楼栋选择');
      this.setData({
        selectedBuildings: [],
        buildingList: this.data.buildingList.map(item => ({ ...item, selected: false }))
      });
      return;
    }

    console.log('开始恢复楼栋选择状态，targetIds:', targetIds);

    // 如果楼栋列表已经存在，直接恢复状态
    if (this.data.buildingList && this.data.buildingList.length > 0) {
      this.doRestoreBuildingSelection(targetIds);
    } else {
      // 先加载楼栋列表，然后恢复状态
      this.loadBuildingList();
      // 等待楼栋列表加载完成后恢复选择状态
      setTimeout(() => {
        this.doRestoreBuildingSelection(targetIds);
      }, 1000);
    }
  },

  // 执行楼栋选择状态恢复
  doRestoreBuildingSelection: function(targetIds) {
    const selectedIds = targetIds.split(',').map(id => id.trim());
    console.log('需要选中的楼栋ID:', selectedIds);
    console.log('当前楼栋列表长度:', this.data.buildingList.length);

    const buildingList = this.data.buildingList.map(building => {
      const isSelected = selectedIds.includes(building.id.toString()) || selectedIds.includes(building.id);
      if (isSelected) {
        console.log('选中楼栋:', building.buildingNumber);
      }
      return {
        ...building,
        selected: isSelected
      };
    });

    const selectedBuildings = buildingList.filter(building => building.selected);

    this.setData({
      buildingList,
      selectedBuildings
    });

    console.log('恢复楼栋选择状态完成，已选楼栋数量:', selectedBuildings.length);
    console.log('已选楼栋:', selectedBuildings.map(b => b.buildingNumber));
  },

  // 选择通知类型
  onTypeChange: function(e) {
    const index = e.detail.value;
    const type = this.data.noticeTypeDict[index];
    
    this.setData({
      typeIndex: index,
      'formData.type': type.nameEn
    });
  },

  // 选择通知对象
  onTargetChange: function(e) {
    const index = e.detail.value;
    const target = this.data.noticeTargetDict[index];
    
    this.setData({
      targetIndex: index,
      'formData.targetType': target.nameEn
    });

    // 如果选择楼栋，加载楼栋列表
    if (target.nameEn === 'building') {
      this.loadBuildingList();
    } else {
      // 如果选择全体业主，设置为当前小区ID
      const selectedCommunity = wx.getStorageSync('selectedCommunity');
      if (selectedCommunity && selectedCommunity.id) {
        this.setData({
          'formData.targetIds': selectedCommunity.id,
          selectedBuildings: []
        });
      }
    }
  },

  // 加载楼栋列表
  loadBuildingList: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: selectedCommunity.id
    };

    communityApi.getPropertyBuildingList(params)
      .then(res => {
        console.log('获取楼栋列表成功', res);

        // 为楼栋添加选中状态
        const buildingList = (res.list || []).map(building => ({
          ...building,
          selected: false
        }));

        this.setData({
          buildingList
        });
      })
      .catch(err => {
        console.error('获取楼栋列表失败', err);
        wx.showToast({
          title: '获取楼栋列表失败',
          icon: 'none'
        });
      });
  },

  // 显示楼栋选择弹窗
  showBuildingSelector: function() {
    console.log('显示楼栋选择弹窗，当前楼栋列表长度:', this.data.buildingList.length);

    if (this.data.buildingList.length === 0) {
      this.loadBuildingList();
    }
    this.setData({
      showBuildingModal: true
    });
  },

  // 关闭楼栋选择弹窗
  closeBuildingModal: function() {
    console.log('关闭楼栋选择弹窗');
    this.setData({
      showBuildingModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 什么都不做，只是阻止事件冒泡
  },

  // 切换楼栋选择状态
  toggleBuildingSelection: function(e) {
    const building = e.currentTarget.dataset.building;

    console.log('点击楼栋:', building.buildingNumber);
    console.log('当前选中状态:', building.selected);

    // 更新楼栋列表中的选中状态
    const buildingList = this.data.buildingList.map(item => {
      if (item.id == building.id) {
        const newSelected = !item.selected;
        console.log('切换楼栋选中状态:', item.buildingNumber, '从', item.selected, '到', newSelected);
        return { ...item, selected: newSelected };
      }
      return item;
    });

    this.setData({
      buildingList: buildingList
    });

    // 实时更新已选楼栋数量显示
    const selectedCount = buildingList.filter(item => item.selected).length;
    console.log('当前已选楼栋数量:', selectedCount);
  },

  // 确认楼栋选择
  confirmBuildingSelection: function() {
    console.log('开始确认楼栋选择');
    console.log('当前楼栋列表:', this.data.buildingList);

    const selectedBuildings = this.data.buildingList.filter(item => {
      console.log('楼栋:', item.buildingNumber, '选中状态:', item.selected);
      return item.selected === true;
    });

    console.log('筛选出的已选楼栋:', selectedBuildings);

    const targetIds = selectedBuildings.map(building => building.id.toString()).join(',');

    console.log('targetIds:', targetIds);

    // 直接设置数据
    this.setData({
      selectedBuildings: selectedBuildings,
      'formData.targetIds': targetIds,
      showBuildingModal: false
    }, () => {
      // 设置完成后的回调
      console.log('数据设置完成');
      console.log('最终selectedBuildings:', this.data.selectedBuildings);
      console.log('最终targetIds:', this.data.formData.targetIds);
    });

    if (selectedBuildings.length > 0) {
      wx.showToast({
        title: `已选择${selectedBuildings.length}个楼栋`,
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '请选择楼栋',
        icon: 'none'
      });
    }
  },

  // 清除楼栋选择（测试用）
  clearBuildingSelection: function() {
    this.setData({
      selectedBuildings: [],
      'formData.targetIds': '',
      buildingList: this.data.buildingList.map(item => ({ ...item, selected: false }))
    });
    wx.showToast({
      title: '已清除选择',
      icon: 'success'
    });
  },

  // 测试加载楼栋
  testLoadBuildings: function() {
    console.log('测试加载楼栋列表');
    this.loadBuildingList();
    wx.showToast({
      title: '正在加载楼栋',
      icon: 'loading'
    });
  },

  // 输入标题
  onTitleInput: function(e) {
    this.setData({
      'formData.title': e.detail.value
    });
  },

  // 输入内容
  onContentInput: function(e) {
    this.setData({
      'formData.content': e.detail.value
    });
  },

  // 切换置顶
  onTopChange: function(e) {
    this.setData({
      'formData.top': e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImage(tempFilePath);
      }
    });
  },

  // 上传图片
  uploadImage: function(filePath) {
    wx.showLoading({
      title: '上传中...'
    });

    commApi.upLoadFile(filePath)
      .then(result => {
        wx.hideLoading();
        console.log('图片上传成功', result);

        if (result.code === 0 && result.data) {
          // 保存服务器返回的路径
          this.setData({
            'formData.imageUrl': result.data
          });

          wx.showToast({
            title: '图片上传成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: result.errorMessage || '图片上传失败',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('图片上传失败', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  // 删除图片
  deleteImage: function() {
    this.setData({
      'formData.imageUrl': ''
    });
  },

  // 保存草稿
  saveDraft: function() {
    if (!this.validateForm(false)) {
      return;
    }

    const params = {
      title: this.data.formData.title,
      type: this.data.formData.type,
      imageUrl: this.data.formData.imageUrl,
      content: this.data.formData.content,
      sort: this.data.formData.sort,
      targetType: this.data.formData.targetType,
      targetIds: this.data.formData.targetIds,
      top: this.data.formData.top
    };

    const apiCall = this.data.formData.id ? 
      noticeApi.updateDraft({ ...params, id: this.data.formData.id }) : 
      noticeApi.addDraft(params);

    wx.showLoading({
      title: '保存中...'
    });

    apiCall.then(res => {
      wx.hideLoading();
      wx.showToast({
        title: '草稿保存成功',
        icon: 'success'
      });

      // 如果是新建草稿，更新ID
      if (!this.data.formData.id && res.id) {
        this.setData({
          'formData.id': res.id
        });
      }

      // 草稿操作只有在来源是草稿箱时才刷新草稿列表
      if (this.data.sourceTab === 'draft') {
        this.markDraftListNeedRefresh();
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('保存草稿失败', err);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },

  // 发布公告
  publishAnnouncement: function() {
    if (!this.validateForm(true)) {
      return;
    }

    wx.showModal({
      title: '确认发布',
      content: '确定要发布这条公告吗？',
      success: (res) => {
        if (res.confirm) {
          this.doPublish();
        }
      }
    });
  },

  // 执行发布
  doPublish: function() {
    const { mode, formData, isDraftEdit } = this.data;
debugger
    const params = {
      content: formData.content,
      imageUrl: formData.imageUrl,
      sort: formData.sort,
      targetIds: formData.targetIds,
      targetType: formData.targetType,
      title: formData.title,
      top: formData.top,
      type: formData.type
    };

    this.setData({ isSubmitting: true });

    let apiMethod;
    let actionText;

    if (mode === 'create') {
      // 创建新通知
      apiMethod = noticeApi.publishNotice;
      actionText = '发布';
    } else if (mode === 'edit' && isDraftEdit) {
      // 编辑草稿 -> 发布通知（不需要传ID，因为是新发布）
      apiMethod = noticeApi.publishNotice;
      actionText = '发布';
    } else if (mode === 'edit' && !isDraftEdit) {
      // 编辑已发布的通知
      params.id = formData.id;
      apiMethod = noticeApi.updateNotice;
      actionText = '保存';
    }

    wx.showLoading({
      title: actionText + '中...'
    });

    apiMethod(params).then(res => {
      wx.hideLoading();
      this.setData({ isSubmitting: false });

      console.log('操作成功', res);

      // 如果是编辑草稿并发布成功，需要删除原草稿
      if (isDraftEdit && formData.id) {
        this.deleteDraftAfterPublish(formData.id);
      }

      // 根据来源tab标记需要刷新的列表
      this.markNeedRefreshBySource();

      wx.showToast({
        title: actionText + '成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }).catch(err => {
      wx.hideLoading();
      this.setData({ isSubmitting: false });

      console.error('操作失败', err);
      wx.showToast({
        title: actionText + '失败，请重试',
        icon: 'none'
      });
    });
  },

  // 发布草稿成功后删除原草稿
  deleteDraftAfterPublish: function(draftId) {
    noticeApi.deleteDraft(draftId)
      .then(() => {
        console.log('草稿删除成功');
      })
      .catch(error => {
        console.error('删除草稿失败', error);
        // 不显示错误提示，因为主要操作（发布）已经成功
      });
  },

  // 标记需要刷新列表
  markNeedRefresh: function() {
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];

      // 如果上一个页面是公告管理页面，标记需要刷新
      if (prevPage.route.includes('announcement/announcement')) {
        prevPage.setData({ needRefresh: true });
      }
    }
  },

  // 根据来源tab标记需要刷新的列表
  markNeedRefreshBySource: function() {
    const pages = getCurrentPages();
    const sourceTab = this.data.sourceTab;

    console.log('标记刷新，来源tab:', sourceTab);

    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];

      // 如果上一个页面是公告管理页面
      if (prevPage.route.includes('announcement/announcement')) {
        if (sourceTab === 'draft') {
          // 来源是草稿箱，标记刷新草稿列表
          prevPage.setData({ needRefreshDraft: true });
          console.log('已标记刷新草稿列表');
        } else {
          // 来源是通知类型，标记刷新通知列表
          prevPage.setData({ needRefresh: true });
          console.log('已标记刷新通知列表');
        }
      }
    }
  },

  // 标记需要刷新草稿列表（兼容旧方法名）
  markDraftListNeedRefresh: function() {
    this.markNeedRefreshBySource();
  },

  // 表单验证
  validateForm: function(isPublish) {
    const { title, content, targetIds } = this.data.formData;

    if (!title.trim()) {
      wx.showToast({
        title: '请输入公告标题',
        icon: 'none'
      });
      return false;
    }

    if (isPublish && !content.trim()) {
      wx.showToast({
        title: '请输入公告内容',
        icon: 'none'
      });
      return false;
    }

    if (!targetIds) {
      wx.showToast({
        title: '请选择发布对象',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 显示草稿箱
  showDraftBox: function() {
    this.loadDraftList();
    this.setData({
      showDraftModal: true
    });
  },

  // 加载草稿列表
  loadDraftList: function() {
    noticeApi.getDraft()
      .then(res => {
        console.log('获取草稿列表成功', res);
        this.setData({
          draftList: res || []
        });
      })
      .catch(err => {
        console.error('获取草稿列表失败', err);
      });
  },

  // 关闭草稿弹窗
  closeDraftModal: function() {
    this.setData({
      showDraftModal: false
    });
  },

  // 选择草稿
  selectDraft: function(e) {
    const draft = e.currentTarget.dataset.draft;

    console.log('选择草稿:', draft);

    this.setData({
      'formData.id': draft.id,
      'formData.title': draft.title || '',
      'formData.type': draft.type || 'property_notice',
      'formData.content': draft.content || '',
      'formData.imageUrl': draft.imageUrl || '',
      'formData.targetType': draft.targetType || 'community',
      'formData.targetIds': draft.targetIds || '',
      'formData.top': draft.top || false,
      showDraftModal: false
    });

    // 更新选择器索引
    this.updatePickerIndexes();

    // 如果是楼栋类型，需要恢复楼栋选择状态
    if (draft.targetType === 'building' && draft.targetIds) {
      console.log('恢复楼栋选择状态，targetIds:', draft.targetIds);
      this.restoreBuildingSelection(draft.targetIds);
    } else {
      // 清空楼栋选择
      this.setData({
        selectedBuildings: [],
        buildingList: this.data.buildingList.map(item => ({ ...item, selected: false }))
      });
    }
  },

  // 删除草稿
  deleteDraft: function(e) {
    const draftId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个草稿吗？',
      success: (res) => {
        if (res.confirm) {
          noticeApi.deleteDraft(draftId)
            .then(() => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              // 刷新当前页面的草稿列表
              this.loadDraftList();

              // 草稿删除只有在来源是草稿箱时才刷新草稿列表
              if (this.data.sourceTab === 'draft') {
                this.markDraftListNeedRefresh();
              }
            })
            .catch(err => {
              console.error('删除草稿失败', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 更新选择器索引
  updatePickerIndexes: function() {
    const { type, targetType } = this.data.formData;

    // 更新类型索引
    const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === type);
    if (typeIndex >= 0) {
      this.setData({ typeIndex });
    }

    // 更新对象索引
    const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === targetType);
    if (targetIndex >= 0) {
      this.setData({ targetIndex });
    }

    // 如果是楼栋类型，加载楼栋列表
    if (targetType === 'building') {
      this.loadBuildingList();

      // 如果有已选择的楼栋ID，恢复选中状态
      if (this.data.formData.targetIds) {
        const selectedIds = this.data.formData.targetIds.split(',');
        setTimeout(() => {
          const buildingList = this.data.buildingList.map(building => ({
            ...building,
            selected: selectedIds.includes(building.id)
          }));

          const selectedBuildings = buildingList.filter(item => item.selected);

          this.setData({
            buildingList,
            selectedBuildings
          });
        }, 500); // 等待楼栋列表加载完成
      }
    }
  },

  // 页面卸载时检查是否需要标记刷新
  onUnload: function() {
    // 根据来源tab决定是否需要刷新
    this.markNeedRefreshBySource();
  }
});
