/**
 * 公告发布/编辑页
 * 用于创建新公告或编辑现有公告/草稿
 */

const noticeApi = require('../../../../../api/noticeApi');
const communityApi = require('../../../../../api/communityApi');
const commApi = require('../../../../../api/commApi');
const util = require('../../../../../utils/util');
const dateUtil = require('../../../../../utils/dateUtil');

Page({
  data: {
    darkMode: false,
    mode: 'create', // create-创建新公告，edit-编辑公告
    apiUrl: '', // API基础URL
    formData: {
      id: null, // 公告ID，编辑模式下有值
      title: '', // 公告标题
      type: 'property_notice', // 公告类型
      targetType: 'community', // 发布对象类型：community-全体业主, building-楼栋
      targetIds: '', // 目标ID，逗号分隔
      content: '', // 公告内容
      imageUrl: '', // 图片URL
      sort: 0, // 排序
      top: false // 是否置顶
    },
    noticeTypeDict: [], // 通知类型字典
    noticeTargetDict: [], // 通知对象字典
    buildingList: [], // 楼栋列表
    selectedBuildings: [], // 已选楼栋列表
    typeIndex: 0, // 当前选中的公告类型索引
    targetIndex: 0, // 当前选中的发布对象索引
    isSubmitting: false, // 是否正在提交
    showDraftModal: false, // 是否显示草稿弹窗
    draftList: [], // 草稿列表
    showBuildingModal: false, // 是否显示楼栋选择弹窗
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置API URL
    const apiUrl = wx.getStorageSync('apiUrl');
    this.setData({ apiUrl });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: options.mode === 'edit' ? '编辑公告' : '发布公告'
    });

    // 设置模式
    if (options.mode) {
      this.setData({ mode: options.mode });
    }

    // 初始化字典数据
    this.initDictData();

    // 如果是编辑模式，加载公告数据
    if (options.id && options.mode === 'edit') {
      this.loadDraftData(options.id);
    }
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const noticeTypeDict = util.getDictByNameEn('notice_type')[0].children;
      const noticeTargetDict = util.getDictByNameEn('notice_target')[0].children;

      this.setData({
        noticeTypeDict,
        noticeTargetDict
      });

      // 设置默认的targetType为community（全体业主）
      const selectedCommunity = wx.getStorageSync('selectedCommunity');
      if (selectedCommunity && selectedCommunity.id) {
        // 找到community对应的索引
        const communityIndex = noticeTargetDict.findIndex(item => item.nameEn === 'community');

        this.setData({
          'formData.targetType': 'community',
          'formData.targetIds': selectedCommunity.id,
          targetIndex: communityIndex >= 0 ? communityIndex : 0
        });
      }
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载草稿数据（编辑模式）
  loadDraftData: function(draftId) {
    wx.showLoading({
      title: '加载草稿中...',
      mask: true
    });

    // 先获取草稿列表
    noticeApi.getDraftList()
      .then(res => {
        const draftList = res.data || res.list || [];
        console.log('获取草稿列表:', draftList);

        // 根据ID查找对应的草稿
        const draft = draftList.find(item => item.id === draftId);

        if (!draft) {
          wx.hideLoading();
          wx.showToast({
            title: '草稿不存在',
            icon: 'none'
          });
          return;
        }

        console.log('找到草稿:', draft);

        // 设置表单数据
        const formData = {
          id: draft.id,
          title: draft.title || '',
          type: draft.type || 'property_notice',
          targetType: draft.targetType || 'community',
          targetIds: draft.targetIds || '',
          content: draft.content || '',
          imageUrl: draft.imageUrl || '',
          sort: draft.sort || 0,
          top: draft.top || false
        };

        // 设置类型和发布对象的索引
        const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === formData.type);
        const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === formData.targetType);

        this.setData({
          formData,
          selectedBuildings: [],
          typeIndex: typeIndex !== -1 ? typeIndex : 0,
          targetIndex: targetIndex !== -1 ? targetIndex : 0
        });

        // 如果是楼栋类型，需要加载楼栋列表并恢复选择状态
        if (formData.targetType === 'building') {
          this.restoreBuildingSelection(formData.targetIds);
        }

        wx.hideLoading();
      })
      .catch(error => {
        wx.hideLoading();
        console.error('加载草稿数据失败', error);
        wx.showToast({
          title: '加载草稿失败，请重试',
          icon: 'none'
        });
      });
  },

  // 恢复楼栋选择状态（编辑模式）
  restoreBuildingSelection: function(targetIds) {
    if (!targetIds) return;

    console.log('开始恢复楼栋选择状态，targetIds:', targetIds);

    // 先加载楼栋列表
    this.loadBuildingList();

    // 等待楼栋列表加载完成后恢复选择状态
    setTimeout(() => {
      const selectedIds = targetIds.split(',').map(id => id.trim());
      console.log('需要选中的楼栋ID:', selectedIds);
      console.log('当前楼栋列表:', this.data.buildingList);

      const buildingList = this.data.buildingList.map(building => {
        const isSelected = selectedIds.includes(building.id.toString()) || selectedIds.includes(building.id);
        return {
          ...building,
          selected: isSelected
        };
      });

      const selectedBuildings = buildingList.filter(building => building.selected);

      this.setData({
        buildingList,
        selectedBuildings
      });

      console.log('恢复楼栋选择状态完成，已选楼栋:', selectedBuildings);
    }, 1500); // 等待1.5秒确保楼栋列表加载完成
  },

  // 选择通知类型
  onTypeChange: function(e) {
    const index = e.detail.value;
    const type = this.data.noticeTypeDict[index];
    
    this.setData({
      typeIndex: index,
      'formData.type': type.nameEn
    });
  },

  // 选择通知对象
  onTargetChange: function(e) {
    const index = e.detail.value;
    const target = this.data.noticeTargetDict[index];
    
    this.setData({
      targetIndex: index,
      'formData.targetType': target.nameEn
    });

    // 如果选择楼栋，加载楼栋列表
    if (target.nameEn === 'building') {
      this.loadBuildingList();
    } else {
      // 如果选择全体业主，设置为当前小区ID
      const selectedCommunity = wx.getStorageSync('selectedCommunity');
      if (selectedCommunity && selectedCommunity.id) {
        this.setData({
          'formData.targetIds': selectedCommunity.id,
          selectedBuildings: []
        });
      }
    }
  },

  // 加载楼栋列表
  loadBuildingList: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      wx.showToast({
        title: '请先选择小区',
        icon: 'none'
      });
      return;
    }

    const params = {
      pageNum: 1,
      pageSize: 500,
      communityId: selectedCommunity.id
    };

    communityApi.getPropertyBuildingList(params)
      .then(res => {
        console.log('获取楼栋列表成功', res);

        // 为楼栋添加选中状态
        const buildingList = (res.list || []).map(building => ({
          ...building,
          selected: false
        }));

        this.setData({
          buildingList
        });
      })
      .catch(err => {
        console.error('获取楼栋列表失败', err);
        wx.showToast({
          title: '获取楼栋列表失败',
          icon: 'none'
        });
      });
  },

  // 显示楼栋选择弹窗
  showBuildingSelector: function() {
    console.log('显示楼栋选择弹窗，当前楼栋列表长度:', this.data.buildingList.length);

    if (this.data.buildingList.length === 0) {
      this.loadBuildingList();
    }
    this.setData({
      showBuildingModal: true
    });
  },

  // 关闭楼栋选择弹窗
  closeBuildingModal: function() {
    console.log('关闭楼栋选择弹窗');
    this.setData({
      showBuildingModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 什么都不做，只是阻止事件冒泡
  },

  // 切换楼栋选择状态
  toggleBuildingSelection: function(e) {
    const building = e.currentTarget.dataset.building;

    console.log('点击楼栋:', building.buildingNumber);
    console.log('当前选中状态:', building.selected);

    // 更新楼栋列表中的选中状态
    const buildingList = this.data.buildingList.map(item => {
      if (item.id == building.id) {
        const newSelected = !item.selected;
        console.log('切换楼栋选中状态:', item.buildingNumber, '从', item.selected, '到', newSelected);
        return { ...item, selected: newSelected };
      }
      return item;
    });

    this.setData({
      buildingList: buildingList
    });

    // 实时更新已选楼栋数量显示
    const selectedCount = buildingList.filter(item => item.selected).length;
    console.log('当前已选楼栋数量:', selectedCount);
  },

  // 确认楼栋选择
  confirmBuildingSelection: function() {
    console.log('开始确认楼栋选择');
    console.log('当前楼栋列表:', this.data.buildingList);

    const selectedBuildings = this.data.buildingList.filter(item => {
      console.log('楼栋:', item.buildingNumber, '选中状态:', item.selected);
      return item.selected === true;
    });

    console.log('筛选出的已选楼栋:', selectedBuildings);

    const targetIds = selectedBuildings.map(building => building.id.toString()).join(',');

    console.log('targetIds:', targetIds);

    // 直接设置数据
    this.setData({
      selectedBuildings: selectedBuildings,
      'formData.targetIds': targetIds,
      showBuildingModal: false
    }, () => {
      // 设置完成后的回调
      console.log('数据设置完成');
      console.log('最终selectedBuildings:', this.data.selectedBuildings);
      console.log('最终targetIds:', this.data.formData.targetIds);
    });

    if (selectedBuildings.length > 0) {
      wx.showToast({
        title: `已选择${selectedBuildings.length}个楼栋`,
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '请选择楼栋',
        icon: 'none'
      });
    }
  },

  // 清除楼栋选择（测试用）
  clearBuildingSelection: function() {
    this.setData({
      selectedBuildings: [],
      'formData.targetIds': '',
      buildingList: this.data.buildingList.map(item => ({ ...item, selected: false }))
    });
    wx.showToast({
      title: '已清除选择',
      icon: 'success'
    });
  },

  // 测试加载楼栋
  testLoadBuildings: function() {
    console.log('测试加载楼栋列表');
    this.loadBuildingList();
    wx.showToast({
      title: '正在加载楼栋',
      icon: 'loading'
    });
  },

  // 输入标题
  onTitleInput: function(e) {
    this.setData({
      'formData.title': e.detail.value
    });
  },

  // 输入内容
  onContentInput: function(e) {
    this.setData({
      'formData.content': e.detail.value
    });
  },

  // 切换置顶
  onTopChange: function(e) {
    this.setData({
      'formData.top': e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImage(tempFilePath);
      }
    });
  },

  // 上传图片
  uploadImage: function(filePath) {
    wx.showLoading({
      title: '上传中...'
    });

    commApi.upLoadFile(filePath)
      .then(result => {
        wx.hideLoading();
        console.log('图片上传成功', result);

        if (result.code === 0 && result.data) {
          // 保存服务器返回的路径
          this.setData({
            'formData.imageUrl': result.data
          });

          wx.showToast({
            title: '图片上传成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: result.errorMessage || '图片上传失败',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('图片上传失败', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  // 删除图片
  deleteImage: function() {
    this.setData({
      'formData.imageUrl': ''
    });
  },

  // 保存草稿
  saveDraft: function() {
    if (!this.validateForm(false)) {
      return;
    }

    const params = {
      title: this.data.formData.title,
      type: this.data.formData.type,
      imageUrl: this.data.formData.imageUrl,
      content: this.data.formData.content,
      sort: this.data.formData.sort,
      targetType: this.data.formData.targetType,
      targetIds: this.data.formData.targetIds,
      top: this.data.formData.top
    };

    const apiCall = this.data.formData.id ? 
      noticeApi.updateDraft({ ...params, id: this.data.formData.id }) : 
      noticeApi.addDraft(params);

    wx.showLoading({
      title: '保存中...'
    });

    apiCall.then(res => {
      wx.hideLoading();
      wx.showToast({
        title: '草稿保存成功',
        icon: 'success'
      });
      
      // 如果是新建草稿，更新ID
      if (!this.data.formData.id && res.id) {
        this.setData({
          'formData.id': res.id
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('保存草稿失败', err);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    });
  },

  // 发布公告
  publishAnnouncement: function() {
    if (!this.validateForm(true)) {
      return;
    }

    wx.showModal({
      title: '确认发布',
      content: '确定要发布这条公告吗？',
      success: (res) => {
        if (res.confirm) {
          this.doPublish();
        }
      }
    });
  },

  // 执行发布
  doPublish: function() {
    const { mode, formData } = this.data;

    const params = {
      content: formData.content,
      imageUrl: formData.imageUrl,
      sort: formData.sort,
      targetIds: formData.targetIds,
      targetType: formData.targetType,
      title: formData.title,
      top: formData.top,
      type: formData.type
    };

    // 如果是编辑模式，添加ID
    if (mode === 'edit' && formData.id) {
      params.id = formData.id;
    }

    this.setData({ isSubmitting: true });
    wx.showLoading({
      title: mode === 'edit' ? '保存中...' : '发布中...'
    });

    // 根据模式选择API方法
    const apiMethod = mode === 'create' ? noticeApi.addNotice : noticeApi.editNotice;

    apiMethod(params).then(res => {
      wx.hideLoading();
      this.setData({ isSubmitting: false });

      console.log('操作成功', res);

      // 标记需要刷新列表
      this.markNeedRefresh();

      wx.showToast({
        title: mode === 'edit' ? '保存成功' : '发布成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }).catch(err => {
      wx.hideLoading();
      this.setData({ isSubmitting: false });

      console.error('操作失败', err);
      wx.showToast({
        title: mode === 'edit' ? '保存失败，请重试' : '发布失败，请重试',
        icon: 'none'
      });
    });
  },

  // 标记需要刷新列表
  markNeedRefresh: function() {
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];

      // 如果上一个页面是公告管理页面，标记需要刷新
      if (prevPage.route.includes('announcement/announcement')) {
        prevPage.setData({ needRefresh: true });
      }
    }
  },

  // 表单验证
  validateForm: function(isPublish) {
    const { title, content, targetIds } = this.data.formData;

    if (!title.trim()) {
      wx.showToast({
        title: '请输入公告标题',
        icon: 'none'
      });
      return false;
    }

    if (isPublish && !content.trim()) {
      wx.showToast({
        title: '请输入公告内容',
        icon: 'none'
      });
      return false;
    }

    if (!targetIds) {
      wx.showToast({
        title: '请选择发布对象',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 显示草稿箱
  showDraftBox: function() {
    this.loadDraftList();
    this.setData({
      showDraftModal: true
    });
  },

  // 加载草稿列表
  loadDraftList: function() {
    noticeApi.getDraft()
      .then(res => {
        console.log('获取草稿列表成功', res);
        this.setData({
          draftList: res || []
        });
      })
      .catch(err => {
        console.error('获取草稿列表失败', err);
      });
  },

  // 关闭草稿弹窗
  closeDraftModal: function() {
    this.setData({
      showDraftModal: false
    });
  },

  // 选择草稿
  selectDraft: function(e) {
    const draft = e.currentTarget.dataset.draft;
    
    this.setData({
      'formData.id': draft.id,
      'formData.title': draft.title || '',
      'formData.type': draft.type || 'property_notice',
      'formData.content': draft.content || '',
      'formData.imageUrl': draft.imageUrl || '',
      'formData.targetType': draft.targetType || 'community',
      'formData.targetIds': draft.targetIds || '',
      'formData.top': draft.top || false,
      showDraftModal: false
    });

    // 更新选择器索引
    this.updatePickerIndexes();
  },

  // 删除草稿
  deleteDraft: function(e) {
    const draftId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个草稿吗？',
      success: (res) => {
        if (res.confirm) {
          noticeApi.deleteDraft(draftId)
            .then(() => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.loadDraftList();
            })
            .catch(err => {
              console.error('删除草稿失败', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 更新选择器索引
  updatePickerIndexes: function() {
    const { type, targetType } = this.data.formData;

    // 更新类型索引
    const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === type);
    if (typeIndex >= 0) {
      this.setData({ typeIndex });
    }

    // 更新对象索引
    const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === targetType);
    if (targetIndex >= 0) {
      this.setData({ targetIndex });
    }

    // 如果是楼栋类型，加载楼栋列表
    if (targetType === 'building') {
      this.loadBuildingList();

      // 如果有已选择的楼栋ID，恢复选中状态
      if (this.data.formData.targetIds) {
        const selectedIds = this.data.formData.targetIds.split(',');
        setTimeout(() => {
          const buildingList = this.data.buildingList.map(building => ({
            ...building,
            selected: selectedIds.includes(building.id)
          }));

          const selectedBuildings = buildingList.filter(item => item.selected);

          this.setData({
            buildingList,
            selectedBuildings
          });
        }, 500); // 等待楼栋列表加载完成
      }
    }
  }
});
