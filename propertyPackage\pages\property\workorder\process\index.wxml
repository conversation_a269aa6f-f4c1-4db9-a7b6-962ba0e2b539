<!-- pages/property/workorder/process/index.wxml -->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <image src="/images/icons/back.svg" class="nav-icon" />
    </view>
    <view class="nav-title">受理工单</view>
    <view class="nav-action"></view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 工单基本信息 -->
    <view class="order-info-card">
      <view class="order-title">{{workOrder.userDescribe}}</view>
      <view class="order-meta">
        <view class="order-id">工单号：{{workOrder.id}}</view>
        <view class="order-time">创建时间：{{workOrder.createTime}}</view>
        <view class="order-location">位置：{{workOrder.region}}</view>
        <view class="order-resident">报修人：{{workOrder.residentName}} {{workOrder.residentPhone}}</view>
      </view>
    </view>

    <!-- 员工选择 -->
    <view class="staff-section">
      <view class="section-title">选择处理员工 <text class="required">*</text></view>
      <view class="section-tips">请选择一个或多个员工来处理此工单</view>

      <view class="staff-list">
        <view
          wx:for="{{staffList}}"
          wx:key="id"
          class="staff-item {{item.selected ? 'selected' : ''}}"
          bindtap="toggleStaffSelection"
          data-id="{{item.id}}"
        >
          <view class="staff-checkbox">
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text wx:if="{{item.selected}}" class="check-mark">✓</text>
            </view>
          </view>

          <view class="staff-avatar">
            <image src="/images/icons/user.svg" class="avatar-icon" />
          </view>

          <view class="staff-info">
            <view class="staff-name">{{item.name}}</view>
            <view class="staff-details">
              <text class="staff-position">{{item.position}}</text>
              <text class="staff-status status-{{item.workStatus}}">{{item.workStatusText}}</text>
            </view>
          </view>
        </view>

        <view class="empty-staff" wx:if="{{staffList.length === 0}}">
          <text>暂无可分配员工</text>
        </view>
      </view>

      <!-- 已选员工统计 -->
      <view class="selected-summary" wx:if="{{selectedStaffIds.length > 0}}">
        <text>已选择 {{selectedStaffIds.length}} 个员工</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <button class="btn-cancel" bindtap="navigateBack" disabled="{{submitting}}">取消</button>
      <button class="btn-submit" bindtap="submitAccept" loading="{{submitting}}" disabled="{{submitting}}">确认受理</button>
    </view>
  </view>
</view>
