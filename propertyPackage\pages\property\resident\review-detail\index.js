// pages/property/resident/review-detail/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    id: null, // 审核ID
    type: '', // 审核类型：resident_room, resident_vehicle
    reviewData: {}, // 审核数据
    statusOptions: [], // 审核状态选项
    selectedStatus: '', // 选择的审核状态
    showStatusModal: false, // 是否显示状态选择弹窗
    submitting: false, // 是否正在提交
    isLoading: true // 是否正在加载
  },

  onLoad: function(options) {
    const { id, type, reviewData } = options;

    this.setData({
      id: id,
      type: type
    });
    debugger
    // 设置导航栏标题
    let title = '审核详情';
    switch(type) {
      case 'resident_room':
        title = '房产审核';
        break;
      case 'resident_vehicle':
        title = '车辆审核';
        break;
    }

    wx.setNavigationBarTitle({
      title: title
    });

    // 如果有传入审核数据，直接使用
    if (reviewData) {
      try {
        const data = JSON.parse(decodeURIComponent(reviewData));
        this.setData({
          reviewData: data,
          isLoading: false
        });
      } catch (error) {
        console.error('解析审核数据失败', error);
      }
    }

    // 加载审核状态字典
    this.loadStatusOptions();
  },

  // 加载审核状态选项
  loadStatusOptions: function() {
    const { type } = this.data;
    let dictName = '';

    if (type === 'resident_room') {
      dictName = 'resident_status';
    } else if (type === 'resident_vehicle') {
      dictName = 'vehicle_status';
    }

    if (dictName) {
      const statusDict = util.getDictByNameEn(dictName);
      this.setData({
        statusOptions: statusDict || []
      });
    }
  },

  // 显示状态选择弹窗
  showStatusSelector: function() {
    this.setData({
      showStatusModal: true
    });
  },

  // 关闭状态选择弹窗
  closeStatusModal: function() {
    this.setData({
      showStatusModal: false
    });
  },

  // 选择审核状态
  selectStatus: function(e) {
    const status = e.currentTarget.dataset.status;
    const statusItem = this.data.statusOptions.find(item => item.nameEn === status);

    this.setData({
      selectedStatus: status,
      selectedStatusName: statusItem ? statusItem.nameCn : status,
      showStatusModal: false
    });
  },

  // 提交审核
  submitReview: function() {
    if (!this.data.selectedStatus) {
      wx.showToast({
        title: '请选择审核状态',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认审核',
      content: `确定要将审核状态设置为"${this.data.selectedStatusName}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.doSubmitReview();
        }
      }
    });
  },

  // 执行审核提交
  doSubmitReview: function() {
    this.setData({ submitting: true });

    const params = {
      id: this.data.id,
      status: this.data.selectedStatus
    };

    const apiMethod = this.data.type === 'resident_room'
      ? propertyApi.examineResidentRoom
      : propertyApi.examineResidentVehicle;

    apiMethod(params)
      .then(res => {
        console.log('审核提交成功', res);

        wx.showToast({
          title: '审核成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(error => {
        console.error('审核提交失败', error);
        wx.showToast({
          title: '审核失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ submitting: false });
      });
  },

  // 加载审核详情（备用方法）
  loadReviewDetail: function() {
    // 如果需要从服务器重新加载详情数据，可以在这里实现
    console.log('加载审核详情');
  }
});
