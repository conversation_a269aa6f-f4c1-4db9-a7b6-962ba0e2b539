/**
 * 公告列表页
 * 展示已发布及草稿公告，进行管理操作
 */

const noticeApi = require('../../../../api/noticeApi');
const dateUtil = require('../../../../utils/dateUtil');
const util = require('../../../../utils/util');

Page({
  data: {
    darkMode: false,
    activeTab: 'all', // 当前选中的标签：all, property_notice, emer_notice, user_message, draft
    searchValue: '', // 搜索关键词
    announcements: [], // 公告列表
    isLoading: true, // 是否正在加载
    isEmpty: false, // 是否为空状态
    isLoadingMore: false, // 是否正在加载更多
    hasMore: true, // 是否还有更多数据
    pageNum: 1, // 当前页码
    pageSize: 10, // 每页数量
    showActionMenu: false, // 是否显示操作菜单
    currentAnnouncement: null, // 当前操作的公告
    noticeTypeDict: [], // 通知类型字典
    noticeTargetDict: [], // 通知对象字典
    needRefresh: false, // 是否需要刷新列表
    needRefreshDraft: false, // 是否需要刷新草稿列表
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '公告管理'
    });

    // 创建防抖搜索函数
    this.debouncedSearch = this.debounce(this.search, 300);

    // 初始化字典数据
    this.initDictData();

    // 加载公告列表
    this.loadAnnouncements();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const noticeTypeDict = util.getDictByNameEn('notice_type')[0].children;
      const noticeTargetDict = util.getDictByNameEn('notice_target')[0].children;

      this.setData({
        noticeTypeDict,
        noticeTargetDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 页面显示时刷新数据
  onShow: function() {
    // 只有在标记需要刷新时才刷新，避免不必要的刷新
    if (this.data.needRefresh) {
      this.setData({ needRefresh: false });
      this.refreshAnnouncements();
    }

    // 检查是否需要刷新草稿列表
    if (this.data.needRefreshDraft) {
      this.setData({ needRefreshDraft: false });
      console.log('检测到需要刷新草稿列表');

      // 如果当前显示的是草稿箱，刷新草稿列表
      if (this.data.currentTab === 'draft') {
        this.loadDraftList();
      }
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshAnnouncements();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMoreAnnouncements();
    }
  },

  // 加载公告列表
  loadAnnouncements: function() {
    const { activeTab, searchValue, pageNum, pageSize } = this.data;

    this.setData({ isLoading: true });

    // 检查小区信息
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      this.setData({ isLoading: false, isEmpty: true });
      return;
    }

    // 根据标签决定调用哪个API
    if (activeTab === 'draft') {
      // 获取草稿列表
      this.loadDraftList();
    } else {
      // 获取已发布公告列表
      this.loadPublishedList();
    }
  },

  // 加载已发布公告列表
  loadPublishedList: function() {
    const { activeTab, searchValue, pageNum, pageSize } = this.data;
    const selectedCommunity = wx.getStorageSync('selectedCommunity');

    // 构建查询参数
    const params = {
      pageNum,
      pageSize,
      communityId: selectedCommunity.id
    };

    // 根据标签设置类型
    if (activeTab !== 'all') {
      params.type = activeTab;
    }

    // 如果有搜索关键词
    if (searchValue) {
      params.title = searchValue;
    }

    // 调用物业获取通知公告列表接口
    noticeApi.getPropertyNoticePage(params)
      .then(res => {
        console.log('获取公告列表成功', res);

        const list = res.list || [];
        const total = res.total || 0;

        // 处理公告数据，添加额外信息
        const announcements = list.map(item => this.processAnnouncementItem(item));

        this.setData({
          announcements,
          isLoading: false,
          isEmpty: announcements.length === 0,
          hasMore: list.length >= this.data.pageSize
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      })
      .catch(err => {
        console.error('加载公告列表失败', err);
        this.setData({
          isLoading: false,
          isEmpty: true
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();

        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载草稿列表
  loadDraftList: function() {
    noticeApi.getDraft()
      .then(res => {
        console.log('获取草稿列表成功', res);

        const list = res || [];

        // 处理草稿数据，添加额外信息
        const announcements = list.map(item => this.processAnnouncementItem({
          ...item,
          status: 'draft'
        }));

        this.setData({
          announcements,
          isLoading: false,
          isEmpty: announcements.length === 0,
          hasMore: false // 草稿列表不分页
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      })
      .catch(err => {
        console.error('加载草稿列表失败', err);
        this.setData({
          isLoading: false,
          isEmpty: true
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();

        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 刷新公告列表
  refreshAnnouncements: function() {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadAnnouncements();
    });
  },

  // 加载更多公告
  loadMoreAnnouncements: function() {
    if (!this.data.hasMore || this.data.isLoadingMore || this.data.activeTab === 'draft') return;

    const { activeTab, searchValue, pageNum, pageSize, announcements } = this.data;
    const selectedCommunity = wx.getStorageSync('selectedCommunity');

    this.setData({ isLoadingMore: true });

    // 构建查询参数
    const params = {
      pageNum: pageNum + 1,
      pageSize,
      communityId: selectedCommunity.id
    };

    // 根据标签设置类型
    if (activeTab !== 'all') {
      params.type = activeTab;
    }

    // 如果有搜索关键词
    if (searchValue) {
      params.title = searchValue;
    }

    // 调用物业获取通知公告列表接口
    noticeApi.getPropertyNoticePage(params)
      .then(res => {
        console.log('加载更多公告成功', res);

        const list = res.list || [];

        // 处理公告数据，添加额外信息
        const newAnnouncements = list.map(item => this.processAnnouncementItem(item));

        // 合并公告列表
        const allAnnouncements = [...announcements, ...newAnnouncements];

        this.setData({
          announcements: allAnnouncements,
          pageNum: pageNum + 1,
          isLoadingMore: false,
          hasMore: list.length >= this.data.pageSize
        });
      })
      .catch(err => {
        console.error('加载更多公告失败', err);
        this.setData({
          isLoadingMore: false
        });

        // 显示错误提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 处理公告数据，添加额外信息
  processAnnouncementItem: function(item) {
    // 判断是否为新公告（24小时内发布）
    const isNew = this.isNewAnnouncement(item.createTime);

    // 格式化日期
    let formattedDate;
    if (item.status === 'draft') {
      // 如果是草稿，显示"草稿，保存于XX-XX XX:XX"
      if (item.updateTime) {
        formattedDate = `草稿，保存于${dateUtil.formatTime(new Date(item.updateTime)).substring(5)}`;
      } else if (item.createTime) {
        formattedDate = `草稿，保存于${dateUtil.formatTime(new Date(item.createTime)).substring(5)}`;
      } else {
        formattedDate = "草稿";
      }
    } else {
      // 如果是已发布公告，显示发布日期
      if (item.createTime) {
        formattedDate = dateUtil.formatTime(new Date(item.createTime)).substring(5);
      } else {
        formattedDate = "已发布";
      }
    }

    // 处理内容预览
    let preview = '';
    if (item.content) {
      // 如果内容是HTML格式，去除HTML标签
      if (item.content.includes('<')) {
        preview = item.content.replace(/<[^>]+>/g, '');
      } else {
        preview = item.content;
      }
      // 截取前50个字符作为预览
      preview = preview.substring(0, 50) + (preview.length > 50 ? '...' : '');
    }

    // 返回处理后的数据
    return {
      ...item,
      isNew,
      formattedDate,
      typeText: this.getTypeText(item.type),
      preview
    };
  },

  // 判断是否为新公告（24小时内发布）
  isNewAnnouncement: function(createTime) {
    if (!createTime) return false;

    const now = new Date();
    const create = new Date(createTime);
    const diffHours = (now - create) / (1000 * 60 * 60);

    return diffHours <= 24;
  },

  // 获取公告类型文本
  getTypeText: function(type) {
    const { noticeTypeDict } = this.data;

    if (noticeTypeDict && noticeTypeDict.length > 0) {
      const typeItem = noticeTypeDict.find(item => item.nameEn === type);
      if (typeItem) {
        return typeItem.nameCn;
      }
    }

    // 默认值
    switch (type) {
      case 'property_notice':
        return '物业通知';
      case 'emer_notice':
        return '紧急通知';
      case 'user_message':
        return '用户消息';
      default:
        return '通知';
    }
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        pageNum: 1,
        hasMore: true,
        announcements: []
      }, () => {
        this.loadAnnouncements();
      });
    }
  },

  // 搜索框输入
  onSearchInput: function(e) {
    const value = e.detail.value;
    this.setData({ searchValue: value });
    this.debouncedSearch(value);
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({ searchValue: '' });
    this.refreshAnnouncements();
  },

  // 搜索
  search: function(value) {
    this.setData({
      pageNum: 1,
      hasMore: true
    }, () => {
      this.loadAnnouncements();
    });
  },

  // 防抖函数
  debounce: function(func, wait) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    };
  },

  // 点击公告卡片
  onAnnouncementTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const status = e.currentTarget.dataset.status;

    console.log('点击公告卡片', id, status);

    if (status === 'draft') {
      // 草稿进入编辑页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/announcement/publish/publish?id=${id}&mode=edit&isDraft=true`
      });
    } else {
      // 已发布进入详情页
      wx.navigateTo({
        url: `/propertyPackage/pages/property/announcement/detail/index?id=${id}`
      });

      // 记录查看的公告ID，用于调试
      wx.setStorage({
        key: 'last_viewed_announcement',
        data: id
      });
    }
  },

  // 长按公告卡片或点击更多按钮
  showActionMenu: function(e) {
    const announcement = e.currentTarget.dataset.item;

    this.setData({
      currentAnnouncement: announcement,
      showActionMenu: true
    });
  },

  // 关闭操作菜单
  closeActionMenu: function() {
    this.setData({
      showActionMenu: false,
      currentAnnouncement: null
    });
  },

  // 编辑公告
  editAnnouncement: function() {
    const announcement = this.data.currentAnnouncement;
    const { id, status } = announcement;

    this.closeActionMenu();

    const isDraft = status === 'draft';
    wx.navigateTo({
      url: `/propertyPackage/pages/property/announcement/publish/publish?id=${id}&mode=edit&isDraft=${isDraft}`
    });
  },

  // 删除公告
  deleteAnnouncement: function() {
    const announcement = this.data.currentAnnouncement;
    const { id, title, status } = announcement;

    this.closeActionMenu();

    const isDraft = status === 'draft';
    const itemType = isDraft ? '草稿' : '公告';

    // 二次确认
    wx.showModal({
      title: '确认删除',
      content: `确定要删除${itemType}"${title}"吗？`,
      confirmColor: '#ff8c00',
      success: (res) => {
        if (res.confirm) {
          // 根据类型调用不同的删除API
          const deleteApi = isDraft ? noticeApi.deleteDraft : noticeApi.deleteNotice;

          deleteApi(id)
            .then(() => {
              // 删除成功，刷新列表
              this.refreshAnnouncements();

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error(`删除${itemType}失败`, err);

              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 置顶/取消置顶公告
  togglePinAnnouncement: function() {
    const announcement = this.data.currentAnnouncement;
    const { id, top } = announcement;

    this.closeActionMenu();

    const action = top ? '取消置顶' : '置顶';
    const newTopStatus = !top;

    wx.showLoading({
      title: `${action}中...`
    });

    // 构建编辑参数，只修改top字段
    const editParams = {
      ...announcement,
      top: newTopStatus
    };

    // 调用编辑接口修改置顶状态
    noticeApi.editNotice(editParams)
      .then(() => {
        wx.hideLoading();

        // 操作成功，立即刷新列表
        this.refreshAnnouncements();

        wx.showToast({
          title: `${action}成功`,
          icon: 'success'
        });
      })
      .catch(err => {
        wx.hideLoading();
        console.error(`${action}公告失败`, err);

        wx.showToast({
          title: `${action}失败，请重试`,
          icon: 'none'
        });
      });
  },

  // 撤回公告
  retractAnnouncement: function() {
    const { id, title } = this.data.currentAnnouncement;

    this.closeActionMenu();

    // 二次确认
    wx.showModal({
      title: '确认撤回',
      content: `确定要撤回公告"${title}"吗？`,
      confirmColor: '#ff8c00',
      success: (res) => {
        if (res.confirm) {
          // 调用API撤回公告
          announcementApi.retractAnnouncement(id)
            .then(() => {
              // 撤回成功，刷新列表
              this.refreshAnnouncements();

              wx.showToast({
                title: '撤回成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('撤回公告失败', err);

              wx.showToast({
                title: '撤回失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 跳转到发布页面
  goToPublish: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/announcement/publish/publish?mode=create'
    });
  },

  // 返回上一页方法已移除，使用微信小程序自带的返回功能
});
