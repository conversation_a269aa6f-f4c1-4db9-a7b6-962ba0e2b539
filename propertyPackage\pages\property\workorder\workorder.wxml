<!--工单管理主页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <view class="header">
    <view class="title">工单管理</view>
    <view class="subtitle">高效处理住户报修与投诉</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 工单概览卡片 -->
    <view class="overview-card">
      <view class="card-title">工单概览</view>
      <view class="status-grid">
        <view class="status-item" bindtap="navigateToOrderList" data-status="">
          <view class="status-count">{{statusCounts.total}}</view>
          <view class="status-label">全部工单</view>
        </view>
        <view class="status-item pending" bindtap="navigateToOrderList" data-status="pending">
          <view class="status-count">{{statusCounts.pending}}</view>
          <view class="status-label">待处理</view>
        </view>
        <view class="status-item processing" bindtap="navigateToOrderList" data-status="processing">
          <view class="status-count">{{statusCounts.processing}}</view>
          <view class="status-label">处理中</view>
        </view>
        <view class="status-item completed" bindtap="navigateToOrderList" data-status="completed">
          <view class="status-count">{{statusCounts.completed}}</view>
          <view class="status-label">已完成</view>
        </view>
        <view class="status-item cancelled" bindtap="navigateToOrderList" data-status="cancelled">
          <view class="status-count">{{statusCounts.cancelled}}</view>
          <view class="status-label">已取消</view>
        </view>
      </view>
    </view>

    <!-- 快捷操作卡片 -->
    <view class="action-card">
      <view class="card-title">快捷操作</view>
      <view class="action-grid">
        <view class="action-item" bindtap="navigateToOrderList">
          <view class="action-icon list-icon"></view>
          <view class="action-label">工单列表</view>
        </view>
        <view class="action-item" bindtap="navigateToCreateOrder">
          <view class="action-icon create-icon"></view>
          <view class="action-label">创建工单</view>
        </view>
        <view class="action-item" bindtap="navigateToOrderStats">
          <view class="action-icon stats-icon"></view>
          <view class="action-label">统计分析</view>
        </view>
      </view>
    </view>

    <!-- 最近工单卡片 -->
    <view class="recent-card">
      <view class="card-header">
        <view class="card-title">最近工单</view>
        <view class="view-all" bindtap="navigateToOrderList">查看全部</view>
      </view>

      <!-- 工单列表 -->
      <view class="order-list">
        <view wx:if="{{recentOrders.length === 0}}" class="empty-list">
          <view class="empty-icon"></view>
          <view class="empty-text">暂无工单</view>
        </view>

        <view
          wx:for="{{recentOrders}}"
          wx:key="id"
          class="order-item status-{{item.status}}"
          bindtap="navigateToOrderDetail"
          data-id="{{item.id}}"
        >
          <view class="order-type-tag {{item.type}}">
            {{item.type === 'repair' ? '维修' :
              item.type === 'complaint' ? '投诉' :
              item.type === 'suggestion' ? '建议' : '其他'}}
          </view>
          <view class="order-info">
            <view class="order-title">{{item.userDescribe}}</view>
            <view class="order-meta">
              <text class="order-id">{{item.region}}</text>
              <text class="order-time">{{item.createTime}}</text>
            </view>
          </view>
          <view class="order-status">
            <text class="status-text">{{item.statusName}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
