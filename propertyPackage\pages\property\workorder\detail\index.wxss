/* pages/property/workorder/detail/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #ff8c00; /* 主品牌色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #ff8c00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 详情内容 */
.detail-content {
  flex: 1;

  padding: 0;
  padding-bottom: 80px; /* 为底部固定按钮留出空间 */
}

/* 工单状态卡片 */
.status-card {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
}

.status-pending {
  background-color: #FFF7E6; /* 警告色的浅色背景 */
}

.status-processing {
  background-color: #E6F0FF; /* 主色的浅色背景 */
}

.status-completed {
  background-color: #F6FFED; /* 成功色的浅色背景 */
}

.status-cancelled {
  background-color: #F5F5F5; /* 中性色背景 */
}

.status-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.status-icon {
  width: 32px;
  height: 32px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.status-pending .status-name {
  color: #FAAD14; /* 警告色 */
}

.status-processing .status-name {
  color: #2196F3; /* 信息色 */
}

.status-completed .status-name {
  color: #52C41A; /* 成功色 */
}

.status-cancelled .status-name {
  color: #999999; /* 文本辅助色 */
}

.status-desc {
  font-size: 14px;
  color: #666;
}

/* 物业操作按钮 */
.action-buttons {
  display: flex;

  margin: 16px;
  margin-bottom: 16px;

}

.action-button {
  
  width: 250rpx;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  border-radius: 22px;
  text-align: center;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
}

.action-button.primary {
  background-color: #ff8c00;
  color: #fff;
}

.action-button.primary::after {
  border: none;
}

.action-button.secondary {
  background-color: #fff;
  color: #ff8c00;
  border: 1px solid #ff8c00;
}

.action-button.secondary::after {
  border: none;
}

.action-button.danger {
  background-color: #fff;
  color: #f44336;
  border: 1px solid #f44336;
}

.action-button.danger::after {
  border: none;
}

/* 详情卡片 */
.detail-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #ff8c00; /* 主品牌色 */
  margin-right: 8px;
  border-radius: 2px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #999;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.priority-high {
  color: #F5222D; /* 错误色 */
  font-weight: 500; /* 高优先级加粗 */
}

.priority-medium {
  color: #FAAD14; /* 警告色 */
}

.priority-low {
  color: #999999; /* 文本辅助色 */
}

/* 问题描述 */
.description-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  position: relative;
}

.problem-image, .evaluation-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  border: 1px solid #eee;
  background-color: #f8f8f8;
}

/* 无图片提示 */
.no-images {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  background-color: #f8f8f8;
  border: 1px dashed #ddd;
  border-radius: 4px;
}

.no-images-text {
  font-size: 12px;
  color: #999;
}

/* 报修地址 */
.address-type {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.address-value {
  font-size: 16px;
  color: #333;
}

/* 报修人信息 */
.reporter-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.reporter-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.avatar-icon {
  width: 32px;
  height: 32px;
}

.reporter-detail {
  flex: 1;
}

.reporter-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.reporter-role {
  font-size: 14px;
  color: #666;
}

/* 已分配人员 */
.assigned-persons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.person-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.person-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.person-info {
  flex: 1;
}

.person-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.person-position {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.person-phone {
  font-size: 12px;
  color: #999;
}

.person-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #e0e0e0;
  color: #666;
}

.status-tag.status-wait_process {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-tag.status-processing {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-tag.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

/* 处理进度 */
.progress-timeline {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  bottom: 0;
  width: 1px;
  background-color: #e8e8e8;
}

.timeline-item.last::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ff8c00;
}

/* 时间线状态颜色 */
.timeline-dot.status-wait_process {
  background-color: #ff9800;
}

.timeline-dot.status-accepted {
  background-color: #2196f3;
}

.timeline-dot.status-processing {
  background-color: #2196f3;
}

.timeline-dot.status-pending {
  background-color: #ff9800;
}

.timeline-dot.status-completed {
  background-color: #4caf50;
}

.timeline-dot.status-cancelled {
  background-color: #9e9e9e;
}

.timeline-content {
  padding-left: 12px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-action {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-operator {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-remark {
  font-size: 14px;
  color: #666;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
}

/* 时间线图片 */
.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.timeline-image-item {
  position: relative;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  border: 1px solid #eee;
  background-color: #f8f8f8;
}

/* 评价信息 */
.evaluation-info {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.rating-stars {
  display: flex;
  margin-bottom: 12px;
}

.star-icon {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}

.evaluation-comment {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.evaluation-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 12px;
}

.no-evaluation {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
  color: #999;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 1001;
}

.modal-dialog.large {
  width: 90%;
  max-width: 650rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 24px;
  color: #999;
  line-height: 1;
}

.modal-content {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
}

.modal-btn {
  min-width: 80px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 18px;
  font-size: 14px;
  margin-left: 12px;
}

.modal-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background-color: #ff8c00;
  color: #fff;
}

.modal-btn.danger {
  background-color: #f44336;
  color: #fff;
}

.confirm-message {
  text-align: center;
  font-size: 16px;
  color: #333;
  padding: 16px 0;
}

/* 表单样式 */
.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 100px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 员工列表样式 */
.staff-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.staff-item:last-child {
  border-bottom: none;
}

.staff-item.selected {
  background-color: #fff7e6;
}

.staff-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.staff-position {
  font-size: 12px;
  color: #999;
}

.staff-check {
  width: 24px;
  height: 24px;
}

.check-icon {
  width: 24px;
  height: 24px;
}

/* 上传图片样式 */
.upload-images {
  margin-top: 8px;
}

.upload-image-item {
  position: relative;
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
}

.upload-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
}

.upload-button {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

/* 弹窗按钮样式补充 */
.modal-btn::after {
  border: none;
}

/* 弹窗内容滚动 */
.modal-content {
  max-height: 60vh;
  overflow-y: auto;
}
