// pages/property/workorder/workorder.js
const util = require('@/utils/util.js');
const workOrderApi = require('@/api/workOrderApi.js')


Page({
  data: {
    darkMode: false,
    statusCounts: {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    },
    recentOrders: [],
    loading: true,
    workOrderType: [],
    workOrderStatus: [],
  },

  onLoad: function () {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '工单管理'
    });

    this.getWorkOrderTypeDict()
    this.getWorkOrderStatusDict()
    // 加载工单数据
    this.loadWorkOrderData();
  },

  onShow: function () {
    // 页面显示时刷新数据，确保从详情页返回时数据是最新的
    this.loadWorkOrderData();
  },

  // 获取工单类型字典
  getWorkOrderTypeDict: function () {
    // 工单类型 work_order_type
    // --nameCn维修 nameEn repair
    // --nameCn投诉 nameEn complaint‌
    // --nameCn建议 nameEn suggestion
    // --nameCn其他 nameEn other

    var workOrderType = util.getDictByNameEn('work_order_type')[0].children
    this.setData({
      workOrderType
    })
  },

  // 获取类型显示名称
  getTypeName: function (typeEn) {
    // 先尝试精确匹配
    let type = this.data.workOrderType.find(item => item.nameEn === typeEn)
    console.log(this.data.workOrderType)

    return type ? type.nameCn : typeEn
  },


  // 获取工单状态字典
  getWorkOrderStatusDict: function () {
    // 工单状态 work_order_status
    // --nameCn待处理 nameEn pending
    // --nameCn处理中 nameEn processing
    // --nameCn已完成 nameEn completed
    // --nameCn已取消 nameEn cancelled

    var workOrderStatus = util.getDictByNameEn('work_order_status')[0].children
     
    this.setData({
      workOrderStatus
    })
  },



  // 加载工单数据
  loadWorkOrderData: function () {
    this.setData({ loading: true });

    var params = {
      pageNum: 1,
      pageSize: 10,
      pageSize: 500,
      communityId: wx.getStorageSync('selectedCommunity').id,
    }
    workOrderApi.getPropertyWorkOrderList(params).then(res => {

      if (res) {

        const orders = res.list.map(order => {
          return {
            ...order,

            // 获取状态显示名称
            statusName: this.getStatusName(order.status),
            // 获取类型显示名称
            typeName: this.getTypeName(order.type)
          }
        })

        this.setData({
          recentOrders: orders,
          loading: false
        })
      }
    }).catch(err => {
      this.setData({

        loading: false
      })
      console.error('获取工单列表失败:', err);

    });


    // // 获取工单数据
    // workOrderManager.getWorkOrders()
    //   .then(orders => {
    //     // 计算各状态工单数量
    //     const statusCounts = this.calculateStatusCounts(orders);

    //     // 获取最近的5个工单
    //     const recentOrders = this.getRecentOrders(orders, 5);

    //     this.setData({
    //       statusCounts: statusCounts,
    //       recentOrders: recentOrders,
    //       loading: false
    //     });
    //   })
    //   .catch(error => {
    //     console.error('加载工单数据失败', error);
    //     this.setData({ loading: false });

    //     wx.showToast({
    //       title: '加载失败，请重试',
    //       icon: 'none'
    //     });
    //   });
  },

  // 获取状态显示名称
  getStatusName: function (statusEn) {
     
    const status = this.data.workOrderStatus.find(item => item.nameEn === statusEn)
    return status ? status.nameCn : statusEn
  },


  // 计算各状态工单数量
  calculateStatusCounts: function (orders) {
    const counts = {
      total: orders.length,
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    };

    orders.forEach(order => {
      if (counts[order.status] !== undefined) {
        counts[order.status]++;
      }
    });

    return counts;
  },



  // 导航到工单列表
  navigateToOrderList: function (e) {
    // const status = e.currentTarget.dataset.status || '';
    // wx.navigateTo({
    //   url: `/servicePackage/pages/workorder/list/index?status=${status}`
    // });

    wx.navigateTo({
      url: `/propertyPackage/pages/property/workorder/list/index`
    });

  },

  // 导航到工单统计
  navigateToOrderStats: function () {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/workorder/stats/index'
    });
  },

  // 导航到创建工单
  navigateToCreateOrder: function () {
    wx.navigateTo({
      url: '/servicePackage/pages/repair/repair?from=property'
    });
  },

  // 导航到工单详情
  navigateToOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.id;
  
    // 正常模式下，点击工单会导航到详情页
    wx.navigateTo({
      url: `/propertyPackage/pages/property/workorder/detail/index?id=${orderId}`
    });
  }
});
