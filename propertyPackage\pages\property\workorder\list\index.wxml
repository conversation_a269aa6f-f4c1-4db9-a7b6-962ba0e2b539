<!--工单列表页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}" data-darkmode="{{darkMode}}">
  <!-- 顶部搜索和筛选 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <view class="search-icon"></view>
      <input
        class="search-input"
        placeholder="搜索工单号、关键词"
        value="{{searchValue}}"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <view class="clear-icon" wx:if="{{searchValue}}" bindtap="clearSearch"></view>
      <view class="filter-button-inline" bindtap="toggleFilterPanel">
        <view class="filter-icon"></view>
        <text>筛选</text>
      </view>
    </view>
  </view>

  <!-- 状态标签栏 -->
  <scroll-view class="status-tabs" scroll-x enable-flex show-scrollbar="{{false}}" enhanced="{{true}}" animation="{{statusTabAnimation}}">
    <view
      wx:for="{{statusOptions}}"
      wx:key="value"
      class="status-tab {{currentStatus === item.value ? 'active' : ''}}"
      hover-class="status-tab-hover"
      bindtap="changeStatus"
      data-status="{{item.value}}"
    >
      {{item.label}}
    </view>
  </scroll-view>

  <!-- 工单类型标签栏 -->
  <scroll-view class="type-tabs" scroll-x enable-flex show-scrollbar="{{false}}" enhanced="{{true}}">
    <view
      wx:for="{{typeOptions}}"
      wx:key="value"
      class="type-tab {{currentType === item.value ? 'active' : ''}}"
      hover-class="type-tab-hover"
      bindtap="changeType"
      data-type="{{item.value}}"
    >
      {{item.label}}
    </view>
  </scroll-view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <view class="order-count">共 {{filteredOrders.length}} 个工单</view>
    <view class="toolbar-actions">
      <view class="action-button {{isSelectMode ? 'active' : ''}}" bindtap="toggleSelectMode">
        <text>{{isSelectMode ? '取消' : '批量操作'}}</text>
        <view class="selected-count" wx:if="{{isSelectMode && selectedOrders.length > 0}}">{{selectedOrders.length}}</view>
      </view>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading && currentPage === 1}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 工单列表 -->
  <view class="order-list" wx:else>
    <!-- 普通列表 -->
    <scroll-view scroll-y class="order-scroll-view">
      <!-- 空列表提示 -->
      <view class="empty-container" wx:if="{{filteredOrders.length === 0}}">
        <view class="empty-icon-default"></view>
        <text class="empty-text">暂无工单</text>
      </view>

      <!-- 工单列表项 -->
      <view
        wx:for="{{filteredOrders}}"
        wx:key="id"
        class="order-item status-{{item.status}} {{selectedOrders.includes(item.id) ? 'selected' : ''}}"
        bindtap="navigateToDetail"
        data-id="{{item.id}}"
        bindlongpress="onItemLongPress"
      >
        <!-- 选择框 -->
        <view class="select-box" wx:if="{{isSelectMode}}">
          <view class="checkbox {{selectedOrders.includes(item.id) ? 'checked' : ''}}"></view>
        </view>

        <!-- 工单类型标签 -->
        <view class="order-type-tag {{item.type}}">
          {{item.type === 'repair' ? '维修' :
            item.type === 'complaint' ? '投诉' :
            item.type === 'suggestion' ? '建议' : '其他'}}
        </view>

        <!-- 工单信息 -->
        <view class="order-info">
          <view class="order-title">{{item.content.title}}</view>
          <view class="order-address" wx:if="{{item.address}}">{{item.address}}</view>
          <view class="order-meta">
            <text class="order-id">{{item.id}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
        </view>

        <!-- 工单状态 -->
        <view class="order-status">
          <!-- 高优先级标记 -->
          <view class="priority-badge" wx:if="{{item.priority >= 3}}">
            <view class="priority-indicator priority-{{item.priority}}"></view>
            <text class="priority-text">{{item.priority === 4 ? '紧急' : '重要'}}</text>
          </view>

          <!-- 新工单标记 (假设createTime在24小时内为新工单) -->
          <view class="new-badge" wx:if="{{item.isNew}}">新</view>

          <text class="status-text">{{item.statusName}}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 加载更多 -->
    <view class="loading-more" wx:if="{{loading && currentPage > 1}}">
      <view class="loading-spinner small"></view>
      <view class="loading-text">加载更多...</view>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && filteredOrders.length > 0 && !loading}}">
      没有更多工单了
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel-mask" wx:if="{{showFilterPanel}}" bindtap="closeFilterPanel" catch:touchmove="preventTouchMove"></view>
  <view class="filter-panel {{showFilterPanel ? 'show' : ''}}" catch:touchmove="preventTouchMove">
    <view class="filter-panel-header">
      <text>筛选</text>
      <view class="close-icon" bindtap="closeFilterPanel" hover-class="close-icon-hover">
        <text class="close-text">关闭</text>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">工单状态</view>
      <view class="filter-options">
        <view
          wx:for="{{statusOptions}}"
          wx:key="value"
          class="filter-option {{currentStatus === item.value ? 'active' : ''}}"
          bindtap="changeStatus"
          data-status="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">排序方式</view>
      <view class="filter-options">
        <view
          wx:for="{{sortOptions}}"
          wx:key="value"
          class="filter-option {{currentSort === item.value ? 'active' : ''}}"
          bindtap="changeSort"
          data-sort="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>
  </view>

  <!-- 批量操作工具栏 -->
  <view class="batch-toolbar {{isSelectMode ? 'show' : ''}}" hidden="{{!isSelectMode}}">
    <!-- 顶部区域：全选和已选数量 -->
    <view class="batch-toolbar-top">
      <view class="batch-select" bindtap="toggleSelectAll">
        <view class="checkbox {{selectedOrders.length === filteredOrders.length ? 'checked' : ''}}"></view>
        <text>全选</text>
      </view>
      <view class="selected-count-display" wx:if="{{selectedOrders.length > 0}}">
        已选择 <text class="count-number">{{selectedOrders.length}}</text> 个工单
      </view>
      <view class="selected-count-display empty" wx:else>
        请选择工单
      </view>
    </view>

    <!-- 底部区域：批量操作按钮 -->
    <view class="batch-actions-container">
      <view class="batch-actions">
        <view
          wx:for="{{batchActionOptions}}"
          wx:key="value"
          class="batch-action {{item.value === 'cancel' ? 'danger' : ''}} {{selectedOrders.length === 0 || (item.value === 'complete' && !canComplete) || (item.value === 'cancel' && !canCancel) || (item.value === 'process' && !canProcess) ? 'disabled' : ''}}"
          bindtap="executeBatchAction"
          data-action="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>
  </view>
</view>
