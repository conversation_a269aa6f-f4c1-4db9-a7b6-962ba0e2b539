<wxs module="utils">
  // 获取字典匹配的显示名称
  function getDictDisplayName(dictArray, nameEn) {
    if (!dictArray || !nameEn) return nameEn || '';

    for (var i = 0; i < dictArray.length; i++) {
      if (dictArray[i].nameEn === nameEn) {
        return dictArray[i].nameCn;
      }
    }
    return nameEn;
  }

  // 生成月份标签
  function generateMonthLabels() {
    var labels = [];
    var now = getDate();
    var currentMonth = now.getMonth();
    var currentYear = now.getFullYear();

    for (var i = 11; i >= 0; i--) {
      var month = currentMonth - i;
      var year = currentYear;

      if (month <= 0) {
        month += 12;
        year -= 1;
      }

      labels.push(month + '月');
    }

    return labels;
  }

  module.exports = {
    getDictDisplayName: getDictDisplayName,
    generateMonthLabels: generateMonthLabels
  };
</wxs>

<view class="resident-stats-container">
  <!-- 标题栏 -->
  <view class="stats-header">
    <view class="stats-title">居民统计分析</view>
    <view class="stats-actions">
      <view class="stats-action" bindtap="exportStatistics">
        <text>导出</text>
      </view>
    </view>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-header">
    <view class="tab-item {{activeTab === 'type' ? 'active' : ''}}" bindtap="switchTab" data-tab="type">
      <text>居民类型</text>
    </view>
    <view class="tab-item {{activeTab === 'gender' ? 'active' : ''}}" bindtap="switchTab" data-tab="gender">
      <text>性别分布</text>
    </view>
    <view class="tab-item {{activeTab === 'age' ? 'active' : ''}}" bindtap="switchTab" data-tab="age">
      <text>年龄分布</text>
    </view>
    <view class="tab-item {{activeTab === 'trend' ? 'active' : ''}}" bindtap="switchTab" data-tab="trend">
      <text>增长趋势</text>
    </view>
    <view class="tab-item {{activeTab === 'room' ? 'active' : ''}}" bindtap="switchTab" data-tab="room">
      <text>房屋户型</text>
    </view>
    <view class="tab-item {{activeTab === 'building' ? 'active' : ''}}" bindtap="switchTab" data-tab="building">
      <text>楼栋分布</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 居民类型分布 -->
  <view class="tab-content" wx:if="{{activeTab === 'type' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民类型分布</text>
      </view>
      <view class="card-body">
        <view class="data-list">
          <view wx:for="{{typeCount}}" wx:for-item="value" wx:for-index="key" wx:key="key" class="data-item">
            <view class="data-label">{{utils.getDictDisplayName(residentTypeDict, key)}}</view>
            <view class="data-value">{{value}}人</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 性别分布 -->
  <view class="tab-content" wx:if="{{activeTab === 'gender' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">性别分布</text>
      </view>
      <view class="card-body">
        <view class="data-list">
          <view wx:for="{{genderCount}}" wx:for-item="value" wx:for-index="key" wx:key="key" class="data-item">
            <view class="data-label">{{utils.getDictDisplayName(genderDict, key)}}</view>
            <view class="data-value">{{value}}人</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 年龄分布 -->
  <view class="tab-content" wx:if="{{activeTab === 'age' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">年龄分布</text>
      </view>
      <view class="card-body">
        <view class="data-list">
          <view wx:for="{{ageCount}}" wx:for-item="value" wx:for-index="key" wx:key="key" class="data-item">
            <view class="data-label">{{key}}</view>
            <view class="data-value">{{value}}人</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 增长趋势 -->
  <view class="tab-content" wx:if="{{activeTab === 'trend' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">居民增长趋势</text>
        <text class="card-subtitle">近12个月数据</text>
      </view>
      <view class="card-body">
        <view class="trend-chart">
          <view class="chart-container">
            <view class="chart-bars">
              <view wx:for="{{trendCount}}" wx:key="index" class="chart-bar" style="height: {{item * 10}}rpx;">
                <text class="bar-value">{{item}}</text>
              </view>
            </view>
            <view class="chart-labels">
              <text wx:for="{{utils.generateMonthLabels()}}" wx:key="index" class="chart-label">{{item}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 房屋户型分布 -->
  <view class="tab-content" wx:if="{{activeTab === 'room' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">房屋户型分布</text>
      </view>
      <view class="card-body">
        <view class="data-list">
          <view wx:for="{{roomTypeCount}}" wx:for-item="value" wx:for-index="key" wx:key="key" class="data-item">
            <view class="data-label">{{utils.getDictDisplayName(roomTypeDict, key)}}</view>
            <view class="data-value">{{value}}户</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 楼栋分布 -->
  <view class="tab-content" wx:if="{{activeTab === 'building' && !isLoading}}">
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">楼栋居民分布</text>
      </view>
      <view class="card-body">
        <view class="data-list">
          <view wx:for="{{buildingResidentCount}}" wx:for-item="value" wx:for-index="key" wx:key="key" class="data-item">
            <view class="data-label">{{key}}</view>
            <view class="data-value">{{value}}人</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>