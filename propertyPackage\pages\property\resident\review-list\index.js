// pages/property/resident/review-list/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    activeTab: 'all', // 当前激活的标签：all, resident_room, resident_vehicle
    activeTabName: '全部', // 当前激活的标签名称
    searchText: '', // 搜索文本
    reviews: [], // 审核列表
    hasMore: true, // 是否有更多数据
    isLoading: false, // 是否正在加载
    isLoadingMore: false, // 是否正在加载更多
    pageNum: 1, // 当前页码
    pageSize: 10, // 每页数量
    total: 0, // 总数量
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '信息审核'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载审核列表
    this.loadReviews();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 确保字典数据已加载
      const residentTypeDict = util.getDictByNameEn('resident_type');
      console.log('居民类型字典:', residentTypeDict);
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载审核列表
  loadReviews: function(isRefresh = true) {
    if (isRefresh) {
      this.setData({
        isLoading: true,
        pageNum: 1,
        reviews: [],
        hasMore: true
      });
    } else {
      this.setData({ isLoadingMore: true });
    }

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      this.setData({ isLoading: false, isLoadingMore: false });
      return;
    }

    const params = {
      type: this.data.activeTab === 'all' ? '' : this.data.activeTab,
      residentName: this.data.searchText,
      phone: '',
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: selectedCommunity.id
    };

    propertyApi.getExamineList(params)
      .then(res => {
        console.log('获取审核列表成功', res);
        const data = res || {};
        const list = data.list || [];

        // 处理列表数据
        const processedList = list.map(item => this.processReviewItem(item));

        const currentReviews = isRefresh ? [] : this.data.reviews;
        const newReviews = [...currentReviews, ...processedList];

        this.setData({
          reviews: newReviews,
          total: data.total || 0,
          hasMore: data.hasNextPage || false,
          pageNum: isRefresh ? 2 : this.data.pageNum + 1
        });
      })
      .catch(error => {
        console.error('获取审核列表失败', error);
        wx.showToast({
          title: '获取审核列表失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({
          isLoading: false,
          isLoadingMore: false
        });
        wx.stopPullDownRefresh();
      });
  },

  // 处理审核项数据
  processReviewItem: function(item) {
    // 获取居民类型字典
    const residentTypeDict = util.getDictByNameEn('resident_type');
    const residentTypeItem = residentTypeDict.find(dict => dict.nameEn === item.residentType);

    return {
      id: item.id,
      residentId: item.residentId,
      type: item.type,
      title: item.type === 'resident_room' ? '房产审核' : '车辆审核',
      applicant: item.residentName,
      phone: item.phone,
      residentType: item.residentType,
      residentTypeName: residentTypeItem ? residentTypeItem.nameCn : item.residentType,
      submitTime: item.createTime,
      plateNumber: item.plateNumber,
      buildingNumber: item.buildingNumber,
      roomNumber: item.roomNumber,
      addressText: item.type === 'resident_room'
        ? `${item.buildingNumber}${item.roomNumber}`
        : item.plateNumber,
      iconClass: item.type === 'resident_room' ? 'icon-house' : 'icon-vehicle',
      status: 'pending',
      statusText: '待审核'
    };
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadReviews(true);
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadReviews(false);
    }
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    let tabName = '全部';

    switch(tab) {
      case 'resident_room':
        tabName = '房产审核';
        break;
      case 'resident_vehicle':
        tabName = '车辆审核';
        break;
      default:
        tabName = '全部';
    }

    this.setData({
      activeTab: tab,
      activeTabName: tabName
    });

    // 重新加载审核列表
    this.loadReviews();
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchText: e.detail.value
    });

    // 防抖搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadReviews(true);
    }, 500);
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchText: ''
    });

    // 重新加载审核列表
    this.loadReviews(true);
  },

  // 显示筛选
  showFilter: function() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 查看详情
  viewDetail: function(e) {
    console.log(e)
    const { id, type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/review-detail/index?id=${id}&type=${type}`
    });
  },



  // 显示拒绝原因弹窗
  showRejectModal: function(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showRejectModal: true,
      currentReviewId: id,
      rejectReason: ''
    });
  },

  // 显示通过确认弹窗
  showApproveModal: function(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showApproveModal: true,
      currentReviewId: id
    });
  },

  // 隐藏弹窗
  hideModal: function() {
    this.setData({
      showRejectModal: false,
      showApproveModal: false
    });
  },

  // 输入拒绝原因
  inputRejectReason: function(e) {
    this.setData({
      rejectReason: e.detail.value
    });
  },

  // 确认拒绝
  confirmReject: function() {
    if (!this.data.rejectReason.trim()) {
      wx.showToast({
        title: '请输入拒绝原因',
        icon: 'none'
      });
      return;
    }

    // 模拟拒绝操作
    const reviews = this.data.reviews;
    const index = reviews.findIndex(review => review.id === this.data.currentReviewId);

    if (index !== -1) {
      reviews[index].status = 'rejected';
      reviews[index].statusText = '已拒绝';

      this.setData({
        reviews: reviews,
        showRejectModal: false
      });

      wx.showToast({
        title: '已拒绝审核',
        icon: 'success'
      });
    }
  },

  // 确认通过
  confirmApprove: function() {
    // 模拟通过操作
    const reviews = this.data.reviews;
    const index = reviews.findIndex(review => review.id === this.data.currentReviewId);

    if (index !== -1) {
      reviews[index].status = 'approved';
      reviews[index].statusText = '已通过';

      this.setData({
        reviews: reviews,
        showApproveModal: false
      });

      wx.showToast({
        title: '已通过审核',
        icon: 'success'
      });
    }
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadReviews();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载审核列表
    this.loadReviews();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
