<!-- pages/property/workorder/detail/index.wxml -->
<view class="container">

  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 工单详情内容 -->
  <scroll-view scroll-y class="detail-content" wx:else>
    <!-- 工单状态卡片 -->
    <view class="status-card status-{{workOrder.status}}">
      <!-- <view class="status-icon-container">
        <image
          src="/images/icons/status-{{workOrder.status}}.svg"
          class="status-icon"
        />
      </view> -->
      <view class="status-info">
        <view class="status-name">{{getStatusText(workOrder.status)}}</view>
        <view class="status-desc">
          <block wx:if="{{workOrder.status === 'wait_process'}}">
            工单已提交，等待处理
          </block>
          <block wx:elif="{{workOrder.status === 'accepted'}}">
            工单已受理，等待处理
          </block>
          <block wx:elif="{{workOrder.status === 'processing'}}">
            工单正在处理中
          </block>
          <block wx:elif="{{workOrder.status === 'pending'}}">
            工单已挂起
          </block>
          <block wx:elif="{{workOrder.status === 'completed'}}">
            工单已完成
          </block>
          <block wx:elif="{{workOrder.status === 'cancel'}}">
            工单已取消
          </block>
        </view>
      </view>
    </view>

    <!-- 物业操作按钮 -->
    <view class="action-buttons" wx:if="{{showAcceptBtn || showCancelBtn || showProcessBtn || showPendingBtn || showCompleteBtn}}">
      <!-- 受理工单按钮 -->
      <button wx:if="{{showAcceptBtn}}" class="action-button primary" bindtap="acceptWorkOrder">受理工单</button>

      <!-- 取消工单按钮 -->
      <button wx:if="{{showCancelBtn}}" class="action-button danger" bindtap="cancelWorkOrder">取消工单</button>

      <!-- 处理工单按钮 -->
      <button wx:if="{{showProcessBtn}}" class="action-button primary" bindtap="processWorkOrder">处理工单</button>

      <!-- 挂起工单按钮 -->
      <button wx:if="{{showPendingBtn}}" class="action-button secondary" bindtap="pendingWorkOrder">挂起工单</button>

      <!-- 完成工单按钮 -->
      <button wx:if="{{showCompleteBtn}}" class="action-button primary" bindtap="completeWorkOrder">完成工单</button>
    </view>

    <!-- 工单基本信息 -->
    <view class="detail-card">
      <view class="card-title">工单信息</view>
      <view class="info-item">
        <view class="info-label">工单编号</view>
        <view class="info-value">{{workOrder.id}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">工单类型</view>
        <view class="info-value">{{workOrder.typeName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">创建时间</view>
        <view class="info-value">{{workOrder.createTime}}</view>
      </view>
      <!-- 优先级和截止时间暂时隐藏 -->
      <!-- <view class="info-item">
        <view class="info-label">优先级</view>
        <view class="info-value priority-{{workOrder.priority}}">{{workOrder.priorityName}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">截止时间</view>
        <view class="info-value">{{workOrder.deadline}}</view>
      </view> -->
      <view class="info-item" wx:if="{{workOrder.assignedTo}}">
        <view class="info-label">处理人员</view>
        <view class="info-value">{{workOrder.assignedTo.name}} ({{workOrder.assignedTo.position}})</view>
      </view>
    </view>

    <!-- 问题描述 -->
    <view class="detail-card">
      <view class="card-title">问题描述</view>
      <view class="description-text">{{workOrder.userDescribe}}</view>

      <!-- 问题图片 -->
      <view class="image-list" wx:if="{{workOrder.imageList && workOrder.imageList.length > 0}}">
        <view class="image-item" wx:for="{{workOrder.imageList}}" wx:key="index">
          <image
            src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}"
            class="problem-image"
            mode="aspectFill"
            bindtap="previewImage"
            data-index="{{index}}"
          />
        </view>
      </view>

      <!-- 无图片提示 -->
      <view class="no-images" wx:if="{{!workOrder.imageList || workOrder.imageList.length === 0}}">
        <text class="no-images-text">暂无图片</text>
      </view>
    </view>

    <!-- 报修地址 -->
    <view class="detail-card">
      <view class="card-title">报修地址</view>
      <view class="address-type">
        {{workOrder.regionType === 'house' ? '业主房屋' : '公共区域'}}
      </view>
      <view class="address-value">{{workOrder.region}}</view>
    </view>

    <!-- 报修人信息 -->
    <view class="detail-card">
      <view class="card-title">报修人信息</view>
      <view class="reporter-info">
        <view class="reporter-avatar">
          <image src="/images/icons/user.svg" class="avatar-icon" />
        </view>
        <view class="reporter-detail">
          <view class="reporter-name">{{workOrder.reporterInfo.name}}</view>
          <view class="reporter-role">住户</view>
        </view>
      </view>
      <view class="info-item">
        <view class="info-label">手机号码</view>
        <view class="info-value">{{workOrder.reporterInfo.phone}}</view>
      </view>
      <view class="info-item">
        <view class="info-label">房屋地址</view>
        <view class="info-value">{{workOrder.reporterInfo.address}}</view>
      </view>
    </view>

    <!-- 处理结果 -->
    <view class="detail-card" wx:if="{{workOrder.status === 'completed' && workOrder.completeResult}}">
      <view class="card-title">处理结果</view>
      <view class="description-text">{{workOrder.completeResult}}</view>

      <!-- 处理结果图片 -->
      <view class="image-list" wx:if="{{workOrder.completeImages.length > 0}}">
        <image
          wx:for="{{workOrder.completeImages}}"
          wx:key="index"
          src="{{item}}"
          class="problem-image"
          mode="aspectFill"
          bindtap="previewCompleteImage"
          data-index="{{index}}"
        />
      </view>
    </view>

    <!-- 已分配人员 -->
    <view class="detail-card" wx:if="{{workOrder.assignedPersons && workOrder.assignedPersons.length > 0}}">
      <view class="card-title">已分配人员</view>
      <view class="assigned-persons">
        <view
          wx:for="{{workOrder.assignedPersons}}"
          wx:key="id"
          class="person-item"
        >
          <view class="person-avatar">
            <image src="/images/icons/user.svg" class="avatar-icon" />
          </view>
          <view class="person-info">
            <view class="person-name">{{item.personName}}</view>
            <view class="person-position" wx:if="{{item.personPosition}}">{{item.personPosition}}</view>
            <view class="person-phone" wx:if="{{item.personPhone}}">{{item.personPhone}}</view>
          </view>
          <view class="person-status">
            <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 处理进度 -->
    <view class="detail-card" wx:if="{{workOrder.processedTimeLine && workOrder.processedTimeLine.length > 0}}">
      <view class="card-title">处理进度</view>
      <view class="progress-timeline">
        <view
          wx:for="{{workOrder.processedTimeLine}}"
          wx:key="id"
          class="timeline-item {{index === 0 ? 'first' : ''}} {{index === workOrder.processedTimeLine.length - 1 ? 'last' : ''}}"
        >
          <view class="timeline-dot status-{{item.status}}"></view>
          <view class="timeline-content">
            <view class="timeline-time">{{item.formattedTime}}</view>
            <view class="timeline-action">{{item.statusText}}</view>
            <view class="timeline-operator">操作人：{{item.operatorName}}</view>
            <view class="timeline-remark" wx:if="{{item.note}}">备注：{{item.note}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 评价信息 -->
    <view class="detail-card" wx:if="{{workOrder.status === 'completed'}}">
      <view class="card-title">评价信息</view>

      <!-- 已评价 -->
      <block wx:if="{{workOrder.evaluation}}">
        <view class="evaluation-info">
          <view class="rating-stars">
            <image
              wx:for="{{5}}"
              wx:key="index"
              src="/images/icons/{{index < workOrder.evaluation.rating ? 'star-filled' : 'star-empty'}}.svg"
              class="star-icon"
            />
          </view>
          <view class="evaluation-comment">{{workOrder.evaluation.comment}}</view>
          <view class="evaluation-time">评价时间：{{workOrder.evaluation.time}}</view>

          <!-- 评价图片 -->
          <view class="image-list" wx:if="{{workOrder.evaluation.images.length > 0}}">
            <image
              wx:for="{{workOrder.evaluation.images}}"
              wx:key="index"
              src="{{item}}"
              class="evaluation-image"
              mode="aspectFill"
              bindtap="previewEvaluationImage"
              data-index="{{index}}"
            />
          </view>
        </view>
      </block>

      <!-- 未评价 -->
      <block wx:else>
        <view class="no-evaluation">
          <text>用户暂未评价</text>
        </view>
      </block>
    </view>
  </scroll-view>

  <!-- 处理工单弹窗 -->
  <view class="modal-mask" wx:if="{{showProcessModal}}" bindtap="hideProcessModal"></view>
  <view class="modal-dialog" wx:if="{{showProcessModal}}">
    <view class="modal-header">
      <text class="modal-title">接单处理</text>
      <view class="modal-close" bindtap="hideProcessModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">处理备注</view>
        <textarea class="form-textarea" placeholder="请输入处理备注" value="{{processingRemark}}" bindinput="inputProcessingRemark"></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideProcessModal">取消</button>
      <button class="modal-btn confirm" bindtap="processOrder">确认接单</button>
    </view>
  </view>

  <!-- 分配工单弹窗 -->
  <view class="modal-mask" wx:if="{{showAssignModal}}" bindtap="hideAssignModal"></view>
  <view class="modal-dialog" wx:if="{{showAssignModal}}">
    <view class="modal-header">
      <text class="modal-title">分配工单</text>
      <view class="modal-close" bindtap="hideAssignModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">选择员工</view>
        <view class="staff-list">
          <view
            wx:for="{{staffList}}"
            wx:key="id"
            class="staff-item {{selectedStaffId === item.id ? 'selected' : ''}}"
            bindtap="selectStaff"
            data-id="{{item.id}}"
          >
            <view class="staff-avatar">
              <image src="/images/icons/user.svg" class="avatar-icon" />
            </view>
            <view class="staff-info">
              <view class="staff-name">{{item.name}}</view>
              <view class="staff-position">{{item.department}} - {{item.position}}</view>
            </view>
            <view class="staff-check" wx:if="{{selectedStaffId === item.id}}">
              <image src="/images/icons/check.svg" class="check-icon" />
            </view>
          </view>
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">分配备注</view>
        <textarea class="form-textarea" placeholder="请输入分配备注" value="{{assignRemark}}" bindinput="inputAssignRemark"></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideAssignModal">取消</button>
      <button class="modal-btn confirm" bindtap="assignOrder">确认分配</button>
    </view>
  </view>

  <!-- 完成工单弹窗 -->
  <view class="modal-mask" wx:if="{{showCompleteModal}}" bindtap="hideCompleteModal"></view>
  <view class="modal-dialog large" wx:if="{{showCompleteModal}}">
    <view class="modal-header">
      <text class="modal-title">完成工单</text>
      <view class="modal-close" bindtap="hideCompleteModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">处理结果</view>
        <textarea class="form-textarea" placeholder="请输入处理结果" value="{{completeResult}}" bindinput="inputCompleteResult"></textarea>
      </view>
      <view class="form-item">
        <view class="form-label">上传图片</view>
        <view class="upload-images">
          <view class="image-list">
            <view
              wx:for="{{completeImages}}"
              wx:key="index"
              class="upload-image-item"
            >
              <image src="{{item}}" class="upload-image" mode="aspectFill" />
              <view class="remove-image" bindtap="removeCompleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-button" bindtap="uploadCompleteImages" wx:if="{{completeImages.length < 9}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">上传图片</view>
            </view>
          </view>
        </view>
      </view>
      <view class="form-item">
        <view class="form-label">完成备注</view>
        <textarea class="form-textarea" placeholder="请输入完成备注" value="{{completeRemark}}" bindinput="inputCompleteRemark"></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideCompleteModal">取消</button>
      <button class="modal-btn confirm" bindtap="completeOrder">确认完成</button>
    </view>
  </view>

  <!-- 添加处理记录弹窗 -->
  <view class="modal-mask" wx:if="{{showAddRecordModal}}" bindtap="hideAddRecordModal"></view>
  <view class="modal-dialog" wx:if="{{showAddRecordModal}}">
    <view class="modal-header">
      <text class="modal-title">添加处理记录</text>
      <view class="modal-close" bindtap="hideAddRecordModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">处理动作</view>
        <input class="form-input" placeholder="请输入处理动作" value="{{recordAction}}" bindinput="inputRecordAction" />
      </view>
      <view class="form-item">
        <view class="form-label">处理备注</view>
        <textarea class="form-textarea" placeholder="请输入处理备注" value="{{recordRemark}}" bindinput="inputRecordRemark"></textarea>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideAddRecordModal">取消</button>
      <button class="modal-btn confirm" bindtap="addProcessingRecord">确认添加</button>
    </view>
  </view>

  <!-- 挂起工单弹窗 -->
  <view class="modal-mask" wx:if="{{showPendingModal}}" bindtap="hidePendingModal"></view>
  <view class="modal-dialog large" wx:if="{{showPendingModal}}">
    <view class="modal-header">
      <text class="modal-title">挂起工单</text>
      <view class="modal-close" bindtap="hidePendingModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">挂起原因</view>
        <textarea class="form-textarea" placeholder="请输入挂起原因" value="{{pendingNote}}" bindinput="inputPendingNote"></textarea>
      </view>
      <view class="form-item">
        <view class="form-label">上传图片</view>
        <view class="upload-images">
          <view class="image-list">
            <view wx:for="{{pendingImages}}" wx:key="index" class="upload-image-item">
              <image src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}" class="upload-image" mode="aspectFill" bindtap="previewModalImage" data-index="{{index}}" data-type="pending" />
              <view class="remove-image" bindtap="deletePendingImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-button" bindtap="choosePendingImages" wx:if="{{pendingImages.length < 9}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">上传图片</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hidePendingModal">取消</button>
      <button class="modal-btn confirm" bindtap="submitPendingWorkOrder">确认挂起</button>
    </view>
  </view>

  <!-- 完成工单弹窗 -->
  <view class="modal-mask" wx:if="{{showCompleteModal}}" bindtap="hideCompleteModal"></view>
  <view class="modal-dialog large" wx:if="{{showCompleteModal}}">
    <view class="modal-header">
      <text class="modal-title">完成工单</text>
      <view class="modal-close" bindtap="hideCompleteModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">处理结果</view>
        <textarea class="form-textarea" placeholder="请输入处理结果" value="{{completeNote}}" bindinput="inputCompleteNote"></textarea>
      </view>
      <view class="form-item">
        <view class="form-label">上传图片</view>
        <view class="upload-images">
          <view class="image-list">
            <view wx:for="{{completeImages}}" wx:key="index" class="upload-image-item">
              <image src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}" class="upload-image" mode="aspectFill" bindtap="previewModalImage" data-index="{{index}}" data-type="complete" />
              <view class="remove-image" bindtap="deleteCompleteImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-button" bindtap="chooseCompleteImages" wx:if="{{completeImages.length < 9}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">上传图片</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideCompleteModal">取消</button>
      <button class="modal-btn confirm" bindtap="submitCompleteWorkOrder">确认完成</button>
    </view>
  </view>

  <!-- 取消工单弹窗 -->
  <view class="modal-mask" wx:if="{{showCancelModal}}" bindtap="hideCancelModal"></view>
  <view class="modal-dialog large" wx:if="{{showCancelModal}}">
    <view class="modal-header">
      <text class="modal-title">取消工单</text>
      <view class="modal-close" bindtap="hideCancelModal">×</view>
    </view>
    <view class="modal-content">
      <view class="form-item">
        <view class="form-label">取消原因</view>
        <textarea class="form-textarea" placeholder="请输入取消原因" value="{{cancelNote}}" bindinput="inputCancelNote"></textarea>
      </view>
      <view class="form-item">
        <view class="form-label">上传图片</view>
        <view class="upload-images">
          <view class="image-list">
            <view wx:for="{{cancelImages}}" wx:key="index" class="upload-image-item">
              <image src="{{item.startsWith('http') ? item : (apiUrl + '/common-api/v1/file/' + item)}}" class="upload-image" mode="aspectFill" bindtap="previewModalImage" data-index="{{index}}" data-type="cancel" />
              <view class="remove-image" bindtap="deleteCancelImage" data-index="{{index}}">×</view>
            </view>
            <view class="upload-button" bindtap="chooseCancelImages" wx:if="{{cancelImages.length < 9}}">
              <view class="upload-icon">+</view>
              <view class="upload-text">上传图片</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideCancelModal">取消</button>
      <button class="modal-btn confirm danger" bindtap="submitCancelWorkOrder">确认取消</button>
    </view>
  </view>
</view>
