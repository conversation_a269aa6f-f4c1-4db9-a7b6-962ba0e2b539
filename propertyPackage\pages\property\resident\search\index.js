// pages/property/resident/search/index.js
const util = require('../../../../../utils/util.js')

Page({
  data: {
    searchText: '', // 搜索文本
    activeFilters: [], // 激活的筛选条件
    showAdvancedFilter: false, // 是否显示高级筛选
    residents: [], // 居民列表
    hasMore: false, // 是否有更多数据
    isLoading: false, // 是否正在加载

    // 触摸滑动相关
    startX: 0,
    moveX: 0,

    // 筛选条件
    filters: [
      { id: 'owner', name: '业主', active: false },
      { id: 'tenant', name: '租户', active: false },
      { id: 'verified', name: '已认证', active: false },
      { id: 'unverified', name: '未认证', active: false }
    ],

    // 高级筛选选项
    advancedFilters: {
      buildingOptions: ['全部', '1栋', '2栋', '3栋', '4栋', '5栋'],
      selectedBuilding: '全部',
      timeOptions: ['全部', '最近一周', '最近一月', '最近三月'],
      selectedTime: '全部',
      statusOptions: ['全部', '正常', '已搬出', '黑名单'],
      selectedStatus: '全部'
    }
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民查询'
    });

    // 加载居民列表
    this.loadResidents();
  },

  // 加载居民列表
  loadResidents: function() {
    this.setData({ isLoading: true });

    // 模拟加载数据
    setTimeout(() => {
      // 模拟居民数据
      const residents = [
        {
          id: 1,
          name: '张三',
          phone: '138****1234',
          idNumber: '410******1234',
          status: 'verified',
          statusText: '已认证',
          type: 'owner',
          typeText: '业主',
          address: '3栋2单元502室',
          registerTime: '2023-05-15'
        },
        {
          id: 2,
          name: '李四',
          phone: '139****5678',
          idNumber: '320******5678',
          status: 'verified',
          statusText: '已认证',
          type: 'tenant',
          typeText: '租户',
          address: '5栋1单元301室',
          registerTime: '2023-06-20'
        },
        {
          id: 3,
          name: '王五',
          phone: '137****9012',
          idNumber: '110******9012',
          status: 'unverified',
          statusText: '未认证',
          type: 'owner',
          typeText: '业主',
          address: '2栋3单元1201室',
          registerTime: '2023-07-10'
        },
        {
          id: 4,
          name: '赵六',
          phone: '136****3456',
          idNumber: '440******3456',
          status: 'verified',
          statusText: '已认证',
          type: 'owner',
          typeText: '业主',
          address: '1栋1单元101室',
          registerTime: '2023-08-05'
        },
        {
          id: 5,
          name: '钱七',
          phone: '135****7890',
          idNumber: '330******7890',
          status: 'unverified',
          statusText: '未认证',
          type: 'tenant',
          typeText: '租户',
          address: '4栋2单元802室',
          registerTime: '2023-09-15'
        }
      ];

      // 应用筛选条件
      let filteredResidents = residents;

      // 应用搜索文本
      if (this.data.searchText) {
        const searchText = this.data.searchText.toLowerCase();
        filteredResidents = filteredResidents.filter(resident =>
          resident.name.toLowerCase().includes(searchText) ||
          resident.phone.includes(searchText) ||
          resident.idNumber.includes(searchText) ||
          resident.address.toLowerCase().includes(searchText)
        );
      }

      // 应用快速筛选条件
      if (this.data.activeFilters.length > 0) {
        filteredResidents = filteredResidents.filter(resident => {
          for (const filter of this.data.activeFilters) {
            if (filter === 'owner' && resident.type !== 'owner') return false;
            if (filter === 'tenant' && resident.type !== 'tenant') return false;
            if (filter === 'verified' && resident.status !== 'verified') return false;
            if (filter === 'unverified' && resident.status !== 'unverified') return false;
          }
          return true;
        });
      }

      // 应用高级筛选条件
      if (this.data.showAdvancedFilter) {
        const { selectedBuilding, selectedTime, selectedStatus } = this.data.advancedFilters;

        // 筛选楼栋
        if (selectedBuilding !== '全部') {
          filteredResidents = filteredResidents.filter(resident =>
            resident.address.startsWith(selectedBuilding)
          );
        }

        // 筛选时间
        if (selectedTime !== '全部') {
          const now = new Date();
          let timeLimit = new Date();

          if (selectedTime === '最近一周') {
            timeLimit.setDate(now.getDate() - 7);
          } else if (selectedTime === '最近一月') {
            timeLimit.setMonth(now.getMonth() - 1);
          } else if (selectedTime === '最近三月') {
            timeLimit.setMonth(now.getMonth() - 3);
          }

          filteredResidents = filteredResidents.filter(resident => {
            const registerTime = new Date(resident.registerTime);
            return registerTime >= timeLimit;
          });
        }

        // 筛选状态
        if (selectedStatus !== '全部') {
          // 这里假设状态映射关系
          const statusMap = {
            '正常': 'normal',
            '已搬出': 'moved',
            '黑名单': 'blacklist'
          };

          filteredResidents = filteredResidents.filter(resident =>
            resident.status === statusMap[selectedStatus]
          );
        }
      }

      this.setData({
        residents: filteredResidents,
        hasMore: false,
        isLoading: false
      });
    }, 500);
  },

  // 搜索输入 - 实现输入即搜索
  onSearchInput: function(e) {
    const searchText = e.detail.value;

    this.setData({
      searchText: searchText
    });

    // 使用防抖处理，避免频繁请求
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    this.searchTimer = setTimeout(() => {
      this.loadResidents();
    }, 300); // 300毫秒延迟，提供更好的用户体验
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchText: ''
    });

    this.loadResidents();
  },

  // 切换筛选条件
  toggleFilter: function(e) {
    const filterId = e.currentTarget.dataset.id;
    const filters = this.data.filters;
    const activeFilters = [...this.data.activeFilters];

    // 更新筛选条件状态
    const index = filters.findIndex(filter => filter.id === filterId);
    if (index !== -1) {
      filters[index].active = !filters[index].active;

      // 更新激活的筛选条件列表
      if (filters[index].active) {
        activeFilters.push(filterId);
      } else {
        const removeIndex = activeFilters.indexOf(filterId);
        if (removeIndex !== -1) {
          activeFilters.splice(removeIndex, 1);
        }
      }
    }

    this.setData({
      filters: filters,
      activeFilters: activeFilters
    });

    // 重新加载居民列表
    this.loadResidents();
  },

  // 显示高级筛选
  showAdvancedFilter: function() {
    this.setData({
      showAdvancedFilter: true
    });
  },

  // 隐藏高级筛选
  hideAdvancedFilter: function() {
    this.setData({
      showAdvancedFilter: false
    });
  },

  // 选择高级筛选选项
  selectAdvancedFilter: function(e) {
    const { type, value } = e.currentTarget.dataset;
    const advancedFilters = this.data.advancedFilters;

    switch(type) {
      case 'building':
        advancedFilters.selectedBuilding = value;
        break;
      case 'time':
        advancedFilters.selectedTime = value;
        break;
      case 'status':
        advancedFilters.selectedStatus = value;
        break;
    }

    this.setData({
      advancedFilters: advancedFilters
    });
  },

  // 应用高级筛选
  applyAdvancedFilter: function() {
    this.hideAdvancedFilter();
    this.loadResidents();
  },

  // 重置高级筛选
  resetAdvancedFilter: function() {
    this.setData({
      advancedFilters: {
        buildingOptions: ['全部', '1栋', '2栋', '3栋', '4栋', '5栋'],
        selectedBuilding: '全部',
        timeOptions: ['全部', '最近一周', '最近一月', '最近三月'],
        selectedTime: '全部',
        statusOptions: ['全部', '正常', '已搬出', '黑名单'],
        selectedStatus: '全部'
      }
    });
  },

  // 查看居民详情
  viewResidentDetail: function(e) {
    debugger
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/detail/index?id=${id}`
    });
  },

  // 扫码查询
  scanQRCode: function() {
    wx.scanCode({
      success: (res) => {
        // 处理扫码结果
        console.log('扫码结果:', res);

        // 假设扫码结果是居民ID
        if (res.result) {
          wx.navigateTo({
            url: `/propertyPackage/pages/property/resident/detail/index?id=${res.result}`
          });
        }
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载更多
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadResidents();
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重置筛选条件
    this.setData({
      activeFilters: [],
      filters: this.data.filters.map(filter => ({...filter, active: false}))
    });

    // 重新加载居民列表
    this.loadResidents();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 触摸开始
  touchStart: function(e) {
    if (e.touches.length === 1) {
      this.setData({
        startX: e.touches[0].clientX,
        moveX: 0
      });
    }
  },

  // 触摸移动
  touchMove: function(e) {
    if (e.touches.length === 1) {
      const moveX = e.touches[0].clientX - this.data.startX;
      const index = e.currentTarget.dataset.index;

      // 只允许向左滑动
      if (moveX < 0) {
        const residents = this.data.residents;
        residents[index].style = `transform: translateX(${moveX}px)`;

        this.setData({
          residents: residents,
          moveX: moveX
        });
      }
    }
  },

  // 触摸结束
  touchEnd: function(e) {
    const index = e.currentTarget.dataset.index;
    const residents = this.data.residents;

    // 如果滑动距离超过阈值，显示操作按钮
    if (this.data.moveX < -80) {
      residents[index].style = 'transform: translateX(-160px)';
    } else {
      residents[index].style = 'transform: translateX(0)';
    }

    this.setData({
      residents: residents
    });
  },

  // 阻止滑动穿透
  preventTouchMove: function() {
    return false;
  }
})
