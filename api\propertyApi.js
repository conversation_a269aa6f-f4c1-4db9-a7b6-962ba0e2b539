
const REQUEST = require('@/utils/request.js')

//获取员工列表
function getPersonList(params)
{
 return	REQUEST.request('/manage-api/v1/person/page', 'GET',params, true)
}

//获取员工信息
function personInfo(personId)
{

	return REQUEST.request('/manage-api/v1/person?id='+personId, 'GET',{}, true)
}

function editPerson(personInfo)
{
	return REQUEST.request('/manage-api/v1/person', 'PUT',personInfo, true)
	
}

//获取物业工作台菜单
function getPropertyMenuList(params)
{
	return REQUEST.request('/manage-api/v1/menu/load-menu', 'get',params, true)
}

//获取组织列表
function getOrgTree()
{
	var params={
		pageNum:1,
		pageSize:500
	}
	return REQUEST.request('/manage-api/v1/org/page','GET',params,true)
}


//获取居民总人数和今日新增人数
function getPersonCount(params)
{
	return REQUEST.request('/manage-api/v1/community/member-count','GET',params,true)
}

//获取居民待审核列表(房屋审核,车辆审核)
function getExamineList(params)
{
	return REQUEST.request('/manage-api/v1/community/examine-page','GET',params,true)
}

//居民房产审核
function examineResidentRoom(params)
{
	return REQUEST.request('/manage-api/v1/community/resident/room','PUT',params,true)
}

//居民车辆审核
function examineResidentVehicle(params)
{
	return REQUEST.request('/manage-api/v1/community/vehicle','PUT',params,true)
}

module.exports={
	getPersonList,
	getOrgTree,
	personInfo,
	editPerson,
	getPropertyMenuList,
	getPersonCount,
	getExamineList,
	examineResidentRoom,
	examineResidentVehicle
}