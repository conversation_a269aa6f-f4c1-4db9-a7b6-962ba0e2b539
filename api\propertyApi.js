
const REQUEST = require('@/utils/request.js')

//获取员工列表
function getPersonList(params)
{
 return	REQUEST.request('/manage-api/v1/person/page', 'GET',params, true)
}

//获取员工信息
function personInfo(personId)
{

	return REQUEST.request('/manage-api/v1/person?id='+personId, 'GET',{}, true)
}

function editPerson(personInfo)
{
	return REQUEST.request('/manage-api/v1/person', 'PUT',personInfo, true)
	
}



//获取组织列表
function getOrgTree()
{
	var params={
		pageNum:1,
		pageSize:500
	}
	return REQUEST.request('/manage-api/v1/org/page','GET',params,true)
}



module.exports={
	getPersonList,
	getOrgTree,
	personInfo,
	editPerson
}