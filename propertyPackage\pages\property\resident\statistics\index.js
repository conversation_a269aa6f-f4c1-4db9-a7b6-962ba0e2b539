// pages/property/resident/statistics/index.js
const util = require('../../../../../utils/util.js')
const propertyApi = require('../../../../../api/propertyApi.js')

Page({
  data: {
    // 当前激活的标签
    activeTab: 'type',

    // 是否正在加载
    isLoading: false,

    // 字典数据
    residentTypeDict: [], // 居民类型字典
    genderDict: [], // 性别字典
    roomTypeDict: [], // 房屋户型字典

    // 统计数据
    genderCount: {}, // 性别分布
    buildingResidentCount: {}, // 楼栋居民分布
    ageCount: {}, // 年龄统计
    typeCount: {}, // 居民类型分布
    trendCount: [], // 居民增长数据(近12个月)
    roomTypeCount: {} // 房屋户型分布
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民统计'
    });

    // 初始化字典数据
    this.initDictData();

    // 加载统计数据
    this.loadStatistics();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取字典数据
      const residentTypeDict = util.getDictByNameEn('resident_type');
      const genderDict = util.getDictByNameEn('gender');
      const roomTypeDict = util.getDictByNameEn('room_type');

      this.setData({
        residentTypeDict: residentTypeDict?.[0]?.children || [],
        genderDict: genderDict?.[0]?.children || [],
        roomTypeDict: roomTypeDict?.[0]?.children || []
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: this.data.residentTypeDict.length,
        genderDict: this.data.genderDict.length,
        roomTypeDict: this.data.roomTypeDict.length
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  // 加载统计数据
  loadStatistics: function() {
    this.setData({ isLoading: true });

    const params = {
      communityId: wx.getStorageSync('selectedCommunity').id
    };

    console.log('请求居民统计数据，参数:', params);

    propertyApi.getResidentStatistics(params)
      .then(res => {
        console.log('居民统计数据返回:', res);

        if (res ) {
          this.setData({
            genderCount: res.genderCount || {},
            buildingResidentCount: res.buildingResidentCount || {},
            ageCount: res.ageCount || {},
            typeCount: res.typeCount || {},
            trendCount: res.trendCount || [],
            roomTypeCount: res.roomTypeCount || {},
            isLoading: false
          });
        } else {
          throw new Error('数据格式错误');
        }
      })
      .catch(error => {
        console.error('加载居民统计数据失败:', error);
        wx.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      });
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;

    if (this.data.activeTab !== tab) {
      this.setData({
        activeTab: tab
      });
      console.log('切换到标签:', tab);
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    console.log('下拉刷新居民统计数据');
    this.loadStatistics();
    wx.stopPullDownRefresh();
  },

  // 导出统计数据
  exportStatistics: function() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  }

})