// pages/property/resident/resident.js
const util = require('../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    // 统计数据
    statistics: {
      totalResidents: 256,
      pendingReviews: 5,
      todayNew: 3
    },

    // 最近活动
    activities: [
      {
        id: 1,
        type: 'identity',
        text: '李明提交了实名认证',
        time: '10分钟前',
        iconClass: 'icon-identity'
      },
      {
        id: 2,
        type: 'contact',
        text: '张三更新了联系方式',
        time: '2小时前',
        iconClass: 'icon-contact'
      },
      {
        id: 3,
        type: 'vehicle',
        text: '王五添加了新车辆',
        time: '昨天 14:30',
        iconClass: 'icon-vehicle'
      },
      {
        id: 4,
        type: 'house',
        text: '赵六关联了新房屋',
        time: '昨天 10:15',
        iconClass: 'icon-house'
      },
      {
        id: 5,
        type: 'identity',
        text: '刘七提交了实名认证',
        time: '前天 16:45',
        iconClass: 'icon-identity'
      }
    ],

    // 动画数据
    animationData: {}
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民管理'
    });

    // 添加页面进入动画
    this.animatePageEnter();

    // 模拟加载数据
    this.loadData();
  },

  onShow: function() {
    // 页面显示时可以执行一些刷新操作
    console.log('页面显示');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 停止下拉刷新
    wx.stopPullDownRefresh();
  },

  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },

  // 加载数据
  loadData: function() {
    // 这里可以添加实际的数据加载逻辑
    // 目前使用的是模拟数据
    console.log('加载居民管理数据');
  },

  // 加载数据
  loadData: function() {
    // 这里可以添加实际的数据加载逻辑
    // 目前使用的是模拟数据
    console.log('加载居民管理数据');

    // 模拟异步加载
    setTimeout(() => {
      // 可以在这里更新统计数据
      this.setData({
        'statistics.totalResidents': 256,
        'statistics.pendingReviews': 5,
        'statistics.todayNew': 3
      });
    }, 500);
  },

  // 导航到审核页面
  navigateToReview: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/review-list/index'
    });
  },

  // 导航到搜索页面
  navigateToSearch: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/search/index'
    });
  },

  // 导航到添加居民页面
  navigateToAdd: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/add/index'
    });
  },

  // 导航到统计页面
  navigateToStatistics: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/statistics/index'
    });
  },

  // 导航到活动详情
  navigateToActivity: function(e) {
    const { id, type } = e.currentTarget.dataset;

    // 根据活动类型导航到不同页面
    switch(type) {
      case 'identity':
        wx.navigateTo({
          url: `/propertyPackage/pages/property/resident/review-detail/index?id=${id}&type=identity`
        });
        break;
      case 'house':
        wx.navigateTo({
          url: `/propertyPackage/pages/property/resident/review-detail/index?id=${id}&type=house`
        });
        break;
      case 'vehicle':
        wx.navigateTo({
          url: `/propertyPackage/pages/property/resident/review-detail/index?id=${id}&type=vehicle`
        });
        break;
      case 'contact':
        wx.navigateTo({
          url: `/propertyPackage/pages/property/resident/detail/index?id=${id}`
        });
        break;
      default:
        console.log('未知活动类型');
    }
  }
})
