// pages/property/resident/resident.js
const util = require('../../../../utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    // 统计数据
    statistics: {
      totalResidents: 0,
      todayNew: 0
    },

    // 待审核列表
    pendingList: [],
    isLoading: true,
    pageNum: 1,
    pageSize: 10,

    // 字典数据
    residentTypeDict: [], // 居民类型字典

    // 动画数据
    animationData: {}
  },

  onLoad: function() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '居民管理'
    });

    // 初始化字典数据
    this.initDictData();

    // 添加页面进入动画
    this.animatePageEnter();

    // 加载数据
    this.loadData();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      // 获取字典数据并存储到data中
      const residentTypeDict = util.getDictByNameEn('resident_type')[0].children;

      this.setData({
        residentTypeDict: residentTypeDict || []
      });

      console.log('字典数据初始化完成:', {
        residentTypeDict: residentTypeDict?.length || 0
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
    }
  },

  onShow: function() {
    // 页面显示时可以执行一些刷新操作
    console.log('页面显示');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    // 重新加载数据
    this.loadData();

    // 延迟停止下拉刷新，确保数据加载完成
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面进入动画
  animatePageEnter: function() {
    // 使用微信小程序的动画API
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease',
    });

    // 初始状态
    animation.opacity(0).translateY(30).step({ duration: 0 });
    this.setData({
      animationData: animation.export()
    });

    // 延迟一点执行入场动画
    setTimeout(() => {
      animation.opacity(1).translateY(0).step();
      this.setData({
        animationData: animation.export()
      });
    }, 100);
  },

  // 加载数据
  loadData: function() {
    this.setData({ isLoading: true });

    // 并行加载统计数据和待审核列表
    Promise.all([
      this.loadStatistics(),
      this.loadPendingList()
    ]).finally(() => {
      this.setData({ isLoading: false });
    });
  },

  // 加载统计数据
  loadStatistics: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      return Promise.reject('未选择小区');
    }

    const params = {
      communityId: selectedCommunity.id
    };

    return propertyApi.getPersonCount(params)
      .then(res => {
        console.log('获取统计数据成功', res);
        const data = res || {};

        this.setData({
          'statistics.totalResidents': data.total || 0,
          'statistics.todayNew': data.today || 0
        });
      })
      .catch(error => {
        console.error('获取统计数据失败', error);
        wx.showToast({
          title: '获取统计数据失败',
          icon: 'none'
        });
      });
  },

  // 加载待审核列表
  loadPendingList: function() {
    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择小区');
      return Promise.reject('未选择小区');
    }

    const params = {
      type: '',
      residentName: '',
      phone: '',
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      communityId: selectedCommunity.id
    };

    return propertyApi.getExamineList(params)
      .then(res => {
        console.log('获取待审核列表成功', res);
        const data = res || {};
        const list = data.list || [];
          
        // 处理列表数据，添加字典匹配
        const processedList = list.map(item => this.processListItem(item));

        this.setData({
          pendingList: processedList
        });
      })
      .catch(error => {
        console.error('获取待审核列表失败', error);
        wx.showToast({
          title: '获取待审核列表失败',
          icon: 'none'
        });
      });
  },

  // 处理列表项数据
  processListItem: function(item) {
    // 使用已存储的字典数据进行匹配
    const { residentTypeDict } = this.data;

    // 匹配居民类型
    let residentTypeName = item.residentType || '';
    if (item.residentType && residentTypeDict.length > 0) {
      const residentTypeItem = residentTypeDict.find(dict => dict.nameEn === item.residentType);
      if (residentTypeItem) {
        residentTypeName = residentTypeItem.nameCn;
      }
    }
  
    // 构建房产全名
    let addressText = '';
    if (item.type === 'resident_room') {
      addressText = item.buildingNumber || '';
      if (item.unitNumber) {
        addressText += item.unitNumber;
      }
      addressText += item.roomNumber || '';
    } else if (item.type === 'resident_vehicle') {
      addressText = item.plateNumber || '';
    }

    return {
      ...item,
      residentTypeName: residentTypeName,
      typeText: item.type === 'resident_room' ? '房产审核' : '车辆审核',
      addressText: addressText
    };
  },

  // 导航到审核页面
  navigateToReview: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/review-list/index'
    });
  },

  // 导航到搜索页面
  navigateToSearch: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/search/index'
    });
  },

  // 导航到添加居民页面
  navigateToAdd: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/add/index'
    });
  },

  // 导航到统计页面
  navigateToStatistics: function() {
    wx.navigateTo({
      url: '/propertyPackage/pages/property/resident/statistics/index'
    });
  },

  // 导航到审核详情
  navigateToReviewDetail: function(e) {
    const item = e.currentTarget.dataset.item;

    // 将审核数据编码后传递
    const reviewData = encodeURIComponent(JSON.stringify(item));
      
    wx.navigateTo({
      url: `/propertyPackage/pages/property/resident/review-detail/index?id=${item.id}&type=${item.type}&reviewData=${reviewData}`
    });
  }
})
