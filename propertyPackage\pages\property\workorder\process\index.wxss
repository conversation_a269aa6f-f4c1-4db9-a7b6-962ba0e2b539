/* pages/property/workorder/process/index.wxss */

/* 容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: #ff8c00; /* 主品牌色 */
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
}

.nav-back, .nav-action {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  width: 24px;
  height: 24px;
}

/* 加载中提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #eee;
  border-top-color: #ff8c00; /* 主品牌色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

/* 主要内容 */
.content {
  flex: 1;

  padding: 16px;
  padding-bottom: 100px; /* 为底部按钮留出空间 */
}

/* 工单基本信息卡片 */
.order-info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.order-meta {
  font-size: 14px;
  color: #666;
}

.order-id, .order-time {
  margin-bottom: 8px;
}

.order-status {
  margin-top: 12px;
}

.status-text {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 100px;
  font-size: 12px;
}

.status-pending {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-processing {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-cancelled {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* 处理表单 */
.process-form {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.required {
  color: #f44336;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 12px;
  padding-right: 50px; /* 为模板按钮留出空间 */
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

/* 带模板按钮的输入框 */
.textarea-with-template,
.input-with-template {
  position: relative;
  width: 100%;
}

/* 错误状态 */
.has-error .form-input,
.has-error .form-textarea,
.has-error {
  border-color: #f44336 !important;
}

/* 错误信息 */
.error-message {
  font-size: 12px;
  color: #f44336;
  margin-top: 4px;
}

.template-button {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.template-icon {
  width: 20px;
  height: 20px;
}

.form-textarea {
  width: 100%;
  height: 120px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  padding-right: 50px; /* 为模板按钮留出空间 */
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
}

.form-tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 员工选择区域 */
.staff-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.section-tips {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 员工列表样式 */
.staff-list {
  max-height: 500rpx;
  overflow-y: auto;
}

.staff-name-id {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.staff-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.staff-id {
  font-size: 24rpx;
  color: #999;
}

.staff-gender-age {
  font-size: 24rpx;
  color: #999;
  margin-right: 16rpx;
}

.staff-status-group {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.staff-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 100rpx;
  background-color: #f0f0f0;
  color: #666;
}

.staff-work-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 100rpx;
  background-color: #e8f5e9;
  color: #4caf50;
}

/* 员工状态样式 */
.status-on_job {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-leave {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-resign {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

/* 工作状态样式 */
.work-status-free {
  background-color: #e8f5e9;
  color: #4caf50;
}

.work-status-busy {
  background-color: #fff3e0;
  color: #ff9800;
}

.work-status-leave {
  background-color: #f5f5f5;
  color: #9e9e9e;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: background-color 0.3s;
}

.staff-item:last-child {
  border-bottom: none;
}

.staff-item.selected {
  background-color: #f6ffed;
}

.staff-checkbox {
  margin-right: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.checkbox.checked {
  background-color: #52c41a;
  border-color: #52c41a;
}

.check-mark {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.staff-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-icon {
  width: 48rpx;
  height: 48rpx;
}

.staff-info {
  flex: 1;
}

.staff-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.staff-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.staff-position {
  font-size: 26rpx;
  color: #666;
}

.staff-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  color: #999;
}

.staff-status.status-idle {
  background-color: #f6ffed;
  color: #52c41a;
}

.staff-status.status-busy {
  background-color: #fff2e8;
  color: #fa8c16;
}

.empty-staff {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}

.selected-summary {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f6ffed;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  color: #52c41a;
}

.staff-position {
  font-size: 12px;
  color: #999;
}

.staff-check {
  width: 24px;
  height: 24px;
}

.check-icon {
  width: 24px;
  height: 24px;
}

/* 上传图片样式 */
.upload-images {
  margin-top: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.upload-image-item {
  position: relative;
  width: 80px;
  height: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
  color: #999;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.btn-cancel, .btn-submit {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 16px;
}

.btn-submit {
  background-color: #ff8c00;
  color: #fff;
}

.btn-cancel:active {
  background-color: #e0e0e0;
}

.btn-submit:active {
  background-color: #e67e00;
}

/* 模板选择器 */
.template-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.template-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.template-selector.show {
  transform: translateY(0);
}

.template-selector-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-selector-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.template-selector-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
}

.template-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-templates {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #999;
  font-size: 14px;
}

.template-selector .template-item {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.template-selector .template-item:last-child {
  margin-bottom: 0;
}

.template-selector .template-content {
  width: 100%;
}

.template-selector .template-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.template-selector .template-remark {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
}

.template-selector-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.template-selector-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
  background-color: #f5f5f5;
  color: #666;
}

.template-selector-btn:active {
  background-color: #e0e0e0;
}

/* 暗黑模式样式 */
.darkMode {
  background-color: #1a1a1a;
}

.darkMode .order-info-card,
.darkMode .process-form,
.darkMode .bottom-buttons {
  background-color: #2a2a2a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.darkMode .order-title {
  color: #fff;
}

.darkMode .order-meta,
.darkMode .form-label {
  color: #ccc;
}

.darkMode .form-input,
.darkMode .form-textarea,
.darkMode .staff-list {
  background-color: #333;
  border-color: #444;
  color: #fff;
}

.darkMode .staff-item {
  border-bottom-color: #444;
}

.darkMode .staff-name {
  color: #fff;
}

.darkMode .staff-position,
.darkMode .form-tips {
  color: #999;
}

.darkMode .upload-button {
  background-color: #333;
  border-color: #444;
}

.darkMode .btn-cancel {
  background-color: #333;
  color: #ccc;
}

.darkMode .btn-cancel:active {
  background-color: #444;
}

.darkMode .template-button {
  background-color: #333;
}

.darkMode .template-selector {
  background-color: #2a2a2a;
}

.darkMode .template-selector-header,
.darkMode .template-selector-footer {
  border-color: #333;
}

.darkMode .template-selector-title {
  color: #fff;
}

.darkMode .template-selector .template-item {
  background-color: #333;
}

.darkMode .template-selector .template-text {
  color: #fff;
}

.darkMode .template-selector .template-remark {
  color: #ccc;
  background-color: #444;
}

.darkMode .template-selector-btn {
  background-color: #333;
  color: #ccc;
}

.darkMode .template-selector-btn:active {
  background-color: #444;
}

/* 暗黑模式 - 员工选择 */
.darkMode .staff-section {
  background-color: #2a2a2a;
}

.darkMode .section-title {
  color: #e8e8e8;
}

.darkMode .section-tips {
  color: #999;
}

.darkMode .staff-item {
  border-bottom-color: #444;
}

.darkMode .staff-item.selected {
  background-color: #1f3a1f;
}

.darkMode .staff-name {
  color: #e8e8e8;
}

.darkMode .staff-position {
  color: #ccc;
}

.darkMode .staff-avatar {
  background-color: #333;
}

.darkMode .checkbox {
  border-color: #666;
}

.darkMode .selected-summary {
  background-color: #1f3a1f;
  color: #52c41a;
}
