/**
 * 公告发布/编辑页
 * 用于创建新公告或编辑现有公告/草稿
 */

const noticeApi = require('../../../../../api/noticeApi');
const communityApi = require('../../../../../api/communityApi');
const util = require('../../../../../utils/util');
const dateUtil = require('../../../../../utils/dateUtil');

Page({
  data: {
    darkMode: false,
    mode: 'create', // create-创建新公告，edit-编辑公告
    formData: {
      id: null, // 公告ID，编辑模式下有值
      title: '', // 公告标题
      type: 'property_notice', // 公告类型
      targetType: 'community', // 发布对象类型：community-全体业主, building-楼栋
      targetIds: '', // 目标ID，逗号分隔
      content: '', // 公告内容
      imageUrl: '', // 图片URL
      sort: 0, // 排序
      top: false // 是否置顶
    },
    noticeTypeDict: [], // 通知类型字典
    noticeTargetDict: [], // 通知对象字典
    buildingList: [], // 楼栋列表
    selectedBuildings: [], // 已选楼栋列表
    typeIndex: 0, // 当前选中的公告类型索引
    targetIndex: 0, // 当前选中的发布对象索引
    isSubmitting: false, // 是否正在提交
    showDraftModal: false, // 是否显示草稿弹窗
    draftList: [], // 草稿列表
    days: Array.from({length: 31}, (_, i) => i + 1),
    hours: Array.from({length: 24}, (_, i) => i),
    minutes: Array.from({length: 12}, (_, i) => i * 5),
    datePickerValue: [0, new Date().getMonth(), new Date().getDate() - 1, new Date().getHours(), Math.floor(new Date().getMinutes() / 5)],
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置模式和标题
    const mode = options.mode || 'create';
    const title = mode === 'create' ? '发布公告' : '编辑公告';

    this.setData({ mode });

    wx.setNavigationBarTitle({
      title
    });

    // 设置导航栏颜色
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#ff8c00'
    });

    // 添加右上角菜单按钮
    wx.showNavigationBarLoading();

    // 设置右上角按钮
    wx.setNavigationBarRightButton({
      text: '发布',
      style: {
        color: '#ffffff',
        fontSize: 16
      },
      success: () => {
        wx.hideNavigationBarLoading();
      },
      fail: (err) => {
        console.error('设置导航栏按钮失败', err);
        wx.hideNavigationBarLoading();

        // 如果设置按钮失败，添加页面内的发布按钮
        this.setData({
          showPageButton: true
        });
      }
    });

    // 初始化字典数据
    this.initDictData();

    // 如果是编辑模式，加载公告数据
    if (mode === 'edit' && options.id) {
      this.loadAnnouncementData(options.id);
    } else {
      // 创建模式，尝试加载草稿
      this.loadDraft();
    }

    // 启动自动保存
    this.startAutoSave();
  },

  onUnload: function() {
    // 页面卸载时保存草稿
    this.saveDraft();

    // 清除定时器
    if (this.data.autoSaveTimer) {
      clearInterval(this.data.autoSaveTimer);
    }
  },

  // 监听导航栏按钮点击事件
  onNavigationBarButtonTap: function(e) {
    console.log('导航栏按钮点击', e);
    this.onPublishTap();
  },

  // 监听右上角菜单按钮点击事件
  onNavigationBarRightButtonTap: function() {
    console.log('右上角按钮点击');
    this.onPublishTap();
  },

  // 初始化字典数据
  initDictData: function() {
    try {
      const noticeTypeDict = util.getDictByNameEn('notice_type')[0].children;
      const noticeTargetDict = util.getDictByNameEn('notice_target')[0].children;

      this.setData({
        noticeTypeDict,
        noticeTargetDict
      });
    } catch (error) {
      console.error('初始化字典数据失败', error);
      // 设置默认字典数据
      this.setData({
        noticeTypeDict: [
          { nameEn: 'property_notice', nameCn: '物业通知' },
          { nameEn: 'emer_notice', nameCn: '紧急通知' },
          { nameEn: 'user_message', nameCn: '用户消息' }
        ],
        noticeTargetDict: [
          { nameEn: 'community', nameCn: '全体业主' },
          { nameEn: 'building', nameCn: '指定楼栋' }
        ]
      });
    }

    // 加载楼栋列表
    this.loadBuildingList();
  },

  // 加载楼栋列表
  loadBuildingList: function() {
    communityApi.getPropertyBuildingList()
      .then(res => {
        const buildingList = res.data || res || [];
        this.setData({ buildingList });
      })
      .catch(error => {
        console.error('加载楼栋列表失败', error);
      });
  },

  // 加载公告数据
  loadAnnouncementData: function(id) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    noticeApi.getNotice(id)
      .then(res => {
        const announcement = res.data || res;

        // 处理楼栋数据
        let selectedBuildings = [];
        if (announcement.targetType === 'building' && announcement.targetIds) {
          const buildingIds = announcement.targetIds.split(',').filter(id => id.trim());
          selectedBuildings = buildingIds.map(id => ({
            id: id.trim(),
            name: `楼栋${id}` // 这里只是示例，实际应该显示真实楼栋名称
          }));
        }

        // 设置表单数据
        const formData = {
          id: announcement.id,
          title: announcement.title || '',
          type: announcement.type || 'property_notice',
          targetType: announcement.targetType || 'community',
          targetIds: announcement.targetIds || '',
          content: announcement.content || '',
          imageUrl: announcement.imageUrl || '',
          sort: announcement.sort || 0,
          top: announcement.top || false
        };

        // 设置类型和发布对象的索引
        const typeIndex = this.data.noticeTypeDict.findIndex(item => item.nameEn === formData.type);
        const targetIndex = this.data.noticeTargetDict.findIndex(item => item.nameEn === formData.targetType);

        this.setData({
          formData,
          selectedBuildings,
          typeIndex: typeIndex !== -1 ? typeIndex : 0,
          targetIndex: targetIndex !== -1 ? targetIndex : 0
        });

        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载公告数据失败', err);

        wx.hideLoading();

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 启动自动保存
  startAutoSave: function() {
    // 每30秒自动保存一次
    const timer = setInterval(() => {
      this.saveDraft(true); // 静默保存
    }, 30000);

    this.setData({ autoSaveTimer: timer });
  },

  // 保存草稿
  saveDraft: function(silent = false) {
    const { formData } = this.data;

    // 检查是否有内容需要保存
    if (!formData.title && !formData.content && formData.images.length === 0) {
      return;
    }

    // 保存到本地存储
    wx.setStorage({
      key: 'announcement_draft',
      data: formData,
      success: () => {
        this.setData({ lastSaveTime: new Date() });

        if (!silent) {
          wx.showToast({
            title: '草稿已保存',
            icon: 'success'
          });
        }
      },
      fail: () => {
        if (!silent) {
          wx.showToast({
            title: '保存草稿失败',
            icon: 'none'
          });
        }
      }
    });

    // 如果是编辑模式或已有ID，也保存到服务器
    if (formData.id) {
      announcementApi.saveDraft({
        ...formData,
        status: 'draft'
      }).catch(err => {
        console.error('保存草稿到服务器失败', err);
      });
    }
  },

  // 加载草稿
  loadDraft: function() {
    // 检查URL参数是否包含new=true，如果是则不加载草稿
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options || {};

    if (options.new === 'true') {
      // 如果是新建模式且指定了不加载草稿，则清除本地存储的草稿
      wx.removeStorage({
        key: 'announcement_draft',
        complete: () => {
          console.log('已清除草稿');
        }
      });
      return;
    }

    // 先检查是否有草稿
    wx.getStorage({
      key: 'announcement_draft',
      success: (res) => {
        // 只有在创建模式下才考虑加载草稿
        if (this.data.mode === 'create') {
          const draftData = res.data;

          // 检查草稿是否有内容
          if (draftData && (draftData.title || draftData.content || (draftData.images && draftData.images.length > 0))) {
            // 显示加载草稿提示
            wx.showModal({
              title: '提示',
              content: '检测到有未完成的草稿，是否加载？',
              confirmText: '加载草稿',
              cancelText: '不加载',
              success: (res) => {
                if (res.confirm) {
                  // 用户选择加载草稿
                  this._loadDraftFromStorage(draftData);
                } else {
                  // 用户选择不加载草稿，清除本地存储的草稿
                  wx.removeStorage({
                    key: 'announcement_draft',
                    complete: () => {
                      console.log('已清除草稿');
                    }
                  });
                }
              }
            });
          }
        }
      },
      fail: () => {
        console.log('没有找到草稿');
      }
    });
  },

  // 从存储中加载草稿
  _loadDraftFromStorage: function(draftData) {
    // 如果传入了草稿数据，直接使用
    if (draftData) {
      this._applyDraftData(draftData);
      return;
    }

    // 否则从存储中获取
    wx.getStorage({
      key: 'announcement_draft',
      success: (res) => {
        // 只有在创建模式下才加载草稿
        if (this.data.mode === 'create') {
          const formData = res.data;
          this._applyDraftData(formData);
        }
      },
      fail: () => {
        console.log('没有找到草稿或加载失败');
      }
    });
  },

  // 应用草稿数据
  _applyDraftData: function(formData) {
    // 设置类型和发布对象的索引
    const typeIndex = this.data.typeOptions.findIndex(item => item.id === formData.type);
    const targetIndex = this.data.targetOptions.findIndex(item => item.id === formData.targetScope);

    this.setData({
      formData,
      typeIndex: typeIndex !== -1 ? typeIndex : 0,
      targetIndex: targetIndex !== -1 ? targetIndex : 0
    });

    this.validateForm();

    wx.showToast({
      title: '已加载草稿',
      icon: 'success'
    });
  },

  // 输入标题
  inputTitle: function(e) {
    this.setData({
      'formData.title': e.detail.value
    });
    this.validateForm();
  },

  // 选择公告类型
  selectType: function(e) {
    const index = e.detail.value;
    const type = this.data.typeOptions[index].id;

    this.setData({
      'formData.type': type,
      typeIndex: index
    });
    this.validateForm();
  },

  // 选择发布对象
  selectTarget: function(e) {
    const index = e.detail.value;
    const targetScope = this.data.targetOptions[index].id;

    this.setData({
      'formData.targetScope': targetScope,
      targetIndex: index
    });
    this.validateForm();
  },

  // 选择楼栋
  selectBuildings: function() {
    const { targetBuildingIds } = this.data.formData;

    // 使用新的树形选择器
    wx.navigateTo({
      url: `/pages/property/announcement/building-select/tree-select?selected=${JSON.stringify(targetBuildingIds)}`
    });
  },

  // 接收选择的楼栋
  onBuildingsSelected: function(e) {
    const { buildings, buildingIds, selectedAll } = e.detail;

    // 如果选择了全体住户，更新发布对象为全体业主
    if (selectedAll) {
      this.setData({
        'formData.targetScope': 'all_residents',
        targetIndex: 0,
        selectedBuildings: []
      });
    } else {
      this.setData({
        selectedBuildings: buildings,
        'formData.targetBuildingIds': buildingIds || []
      });
    }

    this.validateForm();
  },

  // 输入内容
  inputContent: function(e) {
    let content = e.detail.value;

    // 处理HTML标签，确保它们是小写的（微信小程序要求）
    if (content && content.includes('<')) {
      content = content.replace(/<([A-Z][A-Z0-9]*)\b[^>]*>/g, function(m) {
        return m.toLowerCase();
      });
      content = content.replace(/<\/([A-Z][A-Z0-9]*)>/g, function(m) {
        return m.toLowerCase();
      });
    }

    this.setData({
      'formData.content': content
    });
    this.validateForm();
  },

  // 格式化工具栏功能
  formatBold: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      const newContent = content.substring(0, selection.start) +
                        '<strong>' + content.substring(selection.start, selection.end) + '</strong>' +
                        content.substring(selection.end);
      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  formatItalic: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      const newContent = content.substring(0, selection.start) +
                        '<em>' + content.substring(selection.start, selection.end) + '</em>' +
                        content.substring(selection.end);
      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  formatUnderline: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      const newContent = content.substring(0, selection.start) +
                        '<u>' + content.substring(selection.start, selection.end) + '</u>' +
                        content.substring(selection.end);
      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  formatList: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      // 将选中文本按行分割
      const selectedText = content.substring(selection.start, selection.end);
      const lines = selectedText.split('\n');

      // 为每行添加列表标记
      const formattedLines = lines.map(line => {
        if (line.trim()) {
          return '<li>' + line.trim() + '</li>';
        }
        return '';
      }).filter(line => line);

      // 组合成HTML列表
      const listHtml = '<ul>\n' + formattedLines.join('\n') + '\n</ul>';

      const newContent = content.substring(0, selection.start) +
                        listHtml +
                        content.substring(selection.end);

      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  formatIndent: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      // 将选中文本按行分割
      const selectedText = content.substring(selection.start, selection.end);
      const lines = selectedText.split('\n');

      // 为每行添加缩进
      const formattedLines = lines.map(line => {
        if (line.trim()) {
          return '&nbsp;&nbsp;&nbsp;&nbsp;' + line;
        }
        return line;
      });

      const newContent = content.substring(0, selection.start) +
                        formattedLines.join('\n') +
                        content.substring(selection.end);

      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  clearFormat: function() {
    const content = this.data.formData.content || '';
    const selection = this.getSelection();

    if (selection) {
      // 获取选中文本
      let selectedText = content.substring(selection.start, selection.end);

      // 移除HTML标签
      selectedText = selectedText.replace(/<[^>]+>/g, '');

      const newContent = content.substring(0, selection.start) +
                        selectedText +
                        content.substring(selection.end);

      this.setData({
        'formData.content': newContent
      });
    } else {
      wx.showToast({
        title: '请先选择文本',
        icon: 'none'
      });
    }
  },

  // 获取文本选择范围（模拟实现，实际小程序可能需要其他方式）
  getSelection: function() {
    // 注意：微信小程序的textarea不支持获取选择范围
    // 这里只是一个模拟实现，实际应用中可能需要其他方式
    // 例如，可以让用户手动输入特殊标记来标识选择范围

    // 简单起见，我们假设用户选择了整个内容
    const content = this.data.formData.content || '';
    if (content) {
      return {
        start: 0,
        end: content.length
      };
    }

    return null;
  },

  // 选择图片
  chooseImage: function() {
    const { images } = this.data.formData;

    // 最多上传9张图片
    const remainCount = 9 - images.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传9张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 上传图片
        this.uploadImages(res.tempFiles);
      }
    });
  },

  // 上传图片
  uploadImages: function(tempFiles) {
    // 不需要解构images，直接使用this.data.formData.images
    const uploadingImages = [...this.data.uploadingImages];

    tempFiles.forEach((file) => {
      // 添加到上传中列表
      const uploadIndex = uploadingImages.length;
      uploadingImages.push({
        path: file.tempFilePath,
        progress: 0,
        error: false
      });

      this.setData({ uploadingImages });

      // 上传图片
      announcementApi.uploadImage(
        file.tempFilePath,
        (progress) => {
          // 更新上传进度
          const updatedUploadingImages = this.data.uploadingImages;
          updatedUploadingImages[uploadIndex].progress = progress;
          this.setData({ uploadingImages: updatedUploadingImages });
        }
      ).then(res => {
        // 上传成功，添加到图片列表
        const updatedImages = [...this.data.formData.images, res.url];
        const updatedUploadingImages = this.data.uploadingImages.filter((_, index) => index !== uploadIndex);

        this.setData({
          'formData.images': updatedImages,
          uploadingImages: updatedUploadingImages
        });

        this.validateForm();
      }).catch(err => {
        // 上传失败
        console.error('上传图片失败', err);

        const updatedUploadingImages = this.data.uploadingImages;
        updatedUploadingImages[uploadIndex].error = true;

        this.setData({ uploadingImages: updatedUploadingImages });

        wx.showToast({
          title: '上传图片失败',
          icon: 'none'
        });
      });
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;

    images.splice(index, 1);

    this.setData({
      'formData.images': images
    });

    this.validateForm();
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.formData.images;

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 切换置顶状态
  togglePin: function(e) {
    this.setData({
      'formData.isPinned': e.detail.value
    });
  },

  // 切换定时发布状态
  toggleScheduled: function(e) {
    const isScheduled = e.detail.value;

    this.setData({
      'formData.isScheduled': isScheduled
    });

    // 如果开启定时发布，设置默认时间
    if (isScheduled && !this.data.formData.scheduledAt) {
      // 设置为当前时间的下一个小时
      const now = new Date();
      const nextHour = new Date(now.getTime() + 60 * 60 * 1000);
      const formattedDate = dateUtil.formatTime(nextHour);

      this.setData({
        'formData.scheduledAt': formattedDate
      });
    }

    this.validateForm();
  },

  // 日期选择器改变
  onDatePickerChange: function(e) {
    const value = e.detail.value;
    const [, monthIndex, dayIndex, hourIndex, minuteIndex] = value;

    // 获取选中的日期时间值
    const month = this.data.months[monthIndex];
    const day = this.data.days[dayIndex];
    const hour = this.data.hours[hourIndex];
    const minute = this.data.minutes[minuteIndex];

    // 格式化日期时间
    const formattedMonth = month < 10 ? `0${month}` : month;
    const formattedDay = day < 10 ? `0${day}` : day;
    const formattedHour = hour < 10 ? `0${hour}` : hour;
    const formattedMinute = minute < 10 ? `0${minute}` : minute;

    const scheduledAt = `${formattedMonth}-${formattedDay} ${formattedHour}:${formattedMinute}`;

    this.setData({
      'formData.scheduledAt': scheduledAt,
      datePickerValue: value
    });

    this.validateForm();
  },

  // 日期选择器列变化
  onDatePickerColumnChange: function(e) {
    const { column, value } = e.detail;
    const datePickerValue = [...this.data.datePickerValue];

    datePickerValue[column] = value;

    // 处理月份和日期的联动
    if (column === 0 || column === 1) {
      const yearIndex = datePickerValue[0];
      const monthIndex = datePickerValue[1];
      const year = this.data.years[yearIndex];
      const month = this.data.months[monthIndex];

      // 获取当月天数
      const daysInMonth = new Date(year, month, 0).getDate();

      // 更新天数数组
      const days = Array.from({length: daysInMonth}, (_, i) => i + 1);

      // 如果当前选择的日期超出了当月的天数，则调整为当月的最后一天
      if (datePickerValue[2] >= days.length) {
        datePickerValue[2] = days.length - 1;
      }

      this.setData({
        days,
        datePickerValue
      });
    }
  },

  // 验证表单
  validateForm: function() {
    const { formData } = this.data;

    // 检查必填项
    const titleValid = formData.title.trim().length > 0;
    const typeValid = !!formData.type;
    const targetValid = formData.targetScope === 'all_residents' ||
                       (formData.targetScope === 'specific_buildings' && formData.targetBuildingIds.length > 0);
    const contentValid = formData.content.trim().length > 0;
    const scheduledValid = !formData.isScheduled || !!formData.scheduledAt;

    // 表单有效性
    const formValid = titleValid && typeValid && targetValid && contentValid && scheduledValid;

    this.setData({ formValid });
  },

  // 显示预览
  showPreview: function() {
    // 验证表单
    this.validateForm();

    if (!this.data.formValid) {
      wx.showToast({
        title: '请完善必填信息后预览',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '生成预览...',
      mask: true
    });

    // 保存预览数据到缓存
    wx.setStorage({
      key: 'announcement_preview',
      data: {
        ...this.data.formData,
        publishTime: new Date().toISOString()
      },
      success: () => {
        wx.hideLoading();

        // 跳转到预览页
        wx.navigateTo({
          url: '/pages/property/announcement/detail/index?preview=true'
        });
      },
      fail: (err) => {
        wx.hideLoading();

        console.error('保存预览数据失败', err);

        wx.showToast({
          title: '预览失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 保存草稿按钮点击
  onSaveDraftTap: function() {
    this.saveDraft();
  },

  // 取消按钮点击
  onCancelTap: function() {
    // 如果表单有内容，弹窗确认
    const { formData } = this.data;

    if (formData.title || formData.content || formData.images.length > 0) {
      wx.showModal({
        title: '确认取消',
        content: '是否放弃已编辑的内容？',
        confirmColor: '#ff8c00',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  // 发布按钮点击
  onPublishTap: function() {
    // 验证表单
    this.validateForm();

    if (!this.data.formValid) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    const { formData } = this.data;
    const isScheduled = formData.isScheduled;
    const actionText = isScheduled ? '定时发布' : '发布';
    let confirmContent = `确定要${actionText}该公告吗？`;

    if (isScheduled) {
      confirmContent = `确定要于 ${formData.scheduledAt} 定时发布该公告吗？`;
    }

    // 二次确认
    wx.showModal({
      title: `确认${actionText}`,
      content: confirmContent,
      confirmColor: '#ff8c00',
      success: (res) => {
        if (res.confirm) {
          this.submitAnnouncement();
        }
      }
    });
  },

  // 提交公告
  submitAnnouncement: function() {
    const { mode, formData } = this.data;

    this.setData({ isSubmitting: true });

    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 构建提交数据
    const submitData = {
      title: formData.title,
      content: formData.content,
      type: formData.type,
      targetScope: formData.targetScope,
      targetBuildingIds: formData.targetScope === 'specific_buildings' ? formData.targetBuildingIds : [],
      imageUrls: formData.images,
      isPinned: formData.isPinned,
      status: formData.isScheduled ? 'scheduled' : 'published',
      scheduledAt: formData.isScheduled ? formData.scheduledAt : null
    };

    // 根据模式选择API方法
    const apiMethod = mode === 'create' ?
      announcementApi.createAnnouncement :
      (data) => announcementApi.updateAnnouncement(formData.id, data);

    // 调用API
    apiMethod(submitData)
      .then(() => {
        wx.hideLoading();

        const actionText = formData.isScheduled ? '定时发布设置' : '发布';

        wx.showToast({
          title: `${actionText}成功！`,
          icon: 'success'
        });

        // 清除草稿
        wx.removeStorage({
          key: 'announcement_draft'
        });

        // 返回列表页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        console.error('提交公告失败', err);

        wx.hideLoading();

        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });

        this.setData({ isSubmitting: false });
      });
  }
});
