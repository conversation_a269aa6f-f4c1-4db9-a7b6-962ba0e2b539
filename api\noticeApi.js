
const REQUEST = require('@/utils/request.js')

//分页查询消息
function getNoticePage(params){
	return REQUEST.request('/users-api/v1/notice/page', 'GET',params, true)
}

//获取消息详情
function getNoticeById(id)
{
	return REQUEST.request('/users-api/v1/notice?id='+id, 'GET',{}, true)
}


//消息已读
function setNoticeRead(id)
{
	return REQUEST.request('/users-api/v1/notice/read?id='+id, 'POST',{}, true)
}


//分页物业获取通知公告列表
function getPropertyNoticePage(params){
	return REQUEST.request('/manage-api/v1/notice/page', 'GET',params, true)
}


//查询我的草稿
function getDraft(){
	return REQUEST.request('/users-api/v1/notice/draft', 'GET',{}, true)
}

//添加草稿
function addDraft(params){
	return REQUEST.request('/users-api/v1/notice/draft', 'POST',params, true)
}

//修改草稿
function updateDraft(params){
	return REQUEST.request('/users-api/v1/notice/draft', 'PUT',params, true)
}

//删除草稿
function deleteDraft(id){
	return REQUEST.request('/users-api/v1/notice/draft?id='+id, 'DELETE',{}, true)
}

//发布公告
function publishNotice(params){
	return REQUEST.request('/manage-api/v1/notice', 'POST',params, true)
}

//编辑公告
function updateNotice(params){
	return REQUEST.request('/manage-api/v1/notice', 'PUT',params, true)
}

//删除公告
function deleteNotice(id)
{
	return REQUEST.request('/manage-api/v1/notice?id='+id, 'DELETE',{}, true)
}

//获取通知公告
function getNotice(id){
	return REQUEST.request('/users-api/v1/notice?id='+id, 'GET',{}, true)
}

module.exports={
	getNoticePage,
	getNoticeById,
	setNoticeRead,
	getPropertyNoticePage,
	getDraft,
	addDraft,
	updateDraft,
	deleteDraft,
	publishNotice,
	updateNotice,
	deleteNotice,
	getNotice
}